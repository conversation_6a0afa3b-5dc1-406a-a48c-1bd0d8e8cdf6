<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="res_partner_grade_data_gold" model="res.partner.grade">
        <field name="name">Gold</field>
        <field name="sequence">1</field>
    </record>
    <record id="res_partner_grade_data_silver" model="res.partner.grade">
        <field name="name">Silver</field>
        <field name="sequence">2</field>
    </record>
    <record id="res_partner_grade_data_bronze" model="res.partner.grade">
        <field name="name">Bronze</field>
        <field name="sequence">3</field>
    </record>
</odoo>
