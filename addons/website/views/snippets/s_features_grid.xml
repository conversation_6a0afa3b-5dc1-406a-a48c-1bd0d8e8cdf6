<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_features_grid" name="Features Grid">
    <section class="s_features_grid pt64 pb64" data-vcss="001">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 s_col_no_bgcolor pb24">
                    <div class="row">
                        <div class="col-lg-12" data-name="Box">
                            <h2 class="h3-fs">Core Features</h2>
                            <h5 class="lead">Essential tools for your success.</h5>
                            <div class="s_hr pt32 pb-2">
                                <hr class="w-100 mx-auto"/>
                            </div>
                        </div>
                        <div class="col-lg-12 py-3" data-name="Box">
                            <i class="fa fa-flag-o rounded bg-o-color-3 s_features_grid_icon float-start flex-shrink-0" role="img"/>
                            <div class="s_features_grid_content mb-0 d-flex flex-column">
                                <h4 class="h5-fs">Change Icons</h4>
                                <p>Double click an icon to replace it with one of your choice.</p>
                            </div>
                        </div>
                        <div class="col-lg-12 py-3" data-name="Box">
                            <i class="fa fa-files-o rounded bg-o-color-3 s_features_grid_icon float-start flex-shrink-0" role="img"/>
                            <div class="s_features_grid_content d-flex flex-column">
                                <h4 class="h5-fs">Duplicate</h4>
                                <p>Duplicate blocks and columns to add more features.</p>
                            </div>
                        </div>
                        <div class="col-lg-12 py-3" data-name="Box">
                            <i class="fa fa-trash-o rounded bg-o-color-3 s_features_grid_icon float-start flex-shrink-0" role="img"/>
                            <div class="s_features_grid_content d-flex flex-column">
                                <h4 class="h5-fs">Delete Blocks</h4>
                                <p>Select and delete blocks to remove features.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 s_col_no_bgcolor pb24">
                    <div class="row">
                        <div class="col-lg-12" data-name="Box">
                            <h2 class="h3-fs">Foundation Package</h2>
                            <h5 class="lead">Everything you need to get started.</h5>
                            <div class="s_hr pt32 pb-2">
                                <hr class="w-100 mx-auto"/>
                            </div>
                        </div>
                        <div class="col-lg-12 py-3" data-name="Box">
                            <i class="fa fa-magic rounded bg-o-color-3 s_features_grid_icon float-start flex-shrink-0" role="img"/>
                            <div class="s_features_grid_content d-flex flex-column">
                                <h4 class="h5-fs">Great Value</h4>
                                <p>Turn every feature into a benefit for your reader.</p>
                            </div>
                        </div>
                        <div class="col-lg-12 py-3" data-name="Box">
                            <i class="fa fa-eyedropper rounded bg-o-color-3 s_features_grid_icon float-start flex-shrink-0" role="img"/>
                            <div class="s_features_grid_content d-flex flex-column">
                                <h4 class="h5-fs">Edit Styles</h4>
                                <p>You can edit colors and backgrounds to highlight features.</p>
                            </div>
                        </div>
                        <div class="col-lg-12 py-3" data-name="Box">
                            <i class="fa fa-picture-o rounded bg-o-color-3 s_features_grid_icon float-start flex-shrink-0" role="img"/>
                            <div class="s_features_grid_content d-flex flex-column">
                                <h4 class="h5-fs">Sample Icons</h4>
                                <p>All these icons are completely free for commercial use.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<record id="website.s_features_grid_000_scss" model="ir.asset">
    <field name="name">Features grid 000 SCSS</field>
    <field name="bundle">web.assets_frontend</field>
    <field name="path">website/static/src/snippets/s_features_grid/000.scss</field>
    <field name="active" eval="False"/>
</record>
<record id="website.s_features_grid_001_scss" model="ir.asset">
    <field name="name">Features grid 001 SCSS</field>
    <field name="bundle">web.assets_frontend</field>
    <field name="path">website/static/src/snippets/s_features_grid/001.scss</field>
</record>

</odoo>
