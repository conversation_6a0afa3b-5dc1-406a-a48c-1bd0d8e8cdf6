# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_restaurant
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
msgid ", Guests:"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
msgid "/ Guest"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.js:0
msgid "18:45 John 4P"
msgstr ""

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_cheeseburger_product_template
msgid ""
"200G Irish Black Angus beef, 9-month matured cheddar cheese, shredded "
"iceberg lettuce, caramelised onions, crushed tomatoes and Chef’s sauce."
msgstr ""

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_bacon_product_template
msgid ""
"200G Irish Black Angus beef, caramelized onions with paprika, chopped "
"iceberg salad, red onions, grilled bacon, tomato sauce, pickles, barbecue "
"sauce"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr "<strong>Navn på område: </strong>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sales:</strong>"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""
"Et restaurantområde repræsenterer et sted hvor kunder betjenes, det er her du kan\n"
"                opsætte og placere bordene."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__active
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "Aktiv"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add Floor"
msgstr "Tilføj etage"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add Table"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add a new floor to get started."
msgstr "Tilføj en ny etage for at komme i gang."

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr "Tilføj en ny restaurant etage"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Add a new table to get started."
msgstr "Tilføj et nyt bord for at komme i gang."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Add a tip"
msgstr "Tilføj et råd"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Add tip after payment"
msgstr "Tilføj drikkepenge efter betaling"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
msgid "Adjust Amount"
msgstr "Juster Mængde"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__set_tip_after_payment
msgid ""
"Adjust the amount authorized by payment terminals to add a tip after the "
"customers left or at the end of the day."
msgstr ""
"Juster mængden autoriseret af betalingsterminaler for at tilføje drikkepenge"
" efter kunderne er gået, eller ved dagen afslutning."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid ""
"Adjust the tax rate based on whether customers are dining in or opting for "
"takeout."
msgstr ""
"Juster momssatsen baseret på om kunderne spiser på stedet eller vælger "
"takeaway."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow Bill Splitting"
msgstr "Tillad opdeling af regning"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__takeaway
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_takeaway
msgid "Allow to create orders for takeaway customers."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Allow to print receipt before payment"
msgstr "Tillad print af kvittering før betaling."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr "Tillader printning af regning før betaling."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__takeaway_fp_id
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_takeaway_fp_id
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Alternative Fiscal Position"
msgstr "Alternativ bogføringsgruppe"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_anchovy
msgid "Anchovy"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_aperol_spritz_product_template
msgid "Aperol Spritz"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr "Udseende"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_search
msgid "Archived"
msgstr "Arkiveret"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Are you sure?"
msgstr "Er du sikker?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Back"
msgstr "Tilbage"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "Baggrundsfarve"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "Baggrundsbillede"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr "Bacon Burger"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_fries
msgid "Belgian fresh homemade fries"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Bill"
msgstr "Faktura"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
msgid "Bill Printing"
msgstr "Udskrivning af regning"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
msgid "Bill Splitting"
msgstr "Deling af regning"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_black_olives
msgid "Black olives"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/order_summary/order_summary.xml:0
msgid "Book table"
msgstr "Bestil bord"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_old_fashioned_product_template
msgid "Bourbon, bitters, sugar, and a twist of citrus zest."
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.burger_drink_combo_product_template
msgid "Burger Menu Combo"
msgstr "Burger Menu Combo"

#. module: pos_restaurant
#: model:pos.payment.method,name:pos_restaurant.payment_method
msgid "Card"
msgstr "Kort"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "Cart"
msgstr "Kurv"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Change Floor Background"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Change table number?"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr "Cheese Burger"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr "Kylling Karry Sandwich"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Clone"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
msgid "Close Tab"
msgstr "Luk Regning"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr "Club Sandwich"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr "Coca-Cola"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.pos_category_cocktails
msgid "Cocktails"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "Farve"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/receipt_screen.xml:0
msgid "Continue"
msgstr "Fortsæt"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_cosmopolitan_product_template
msgid "Cosmopolitan"
msgstr ""

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_cosmopolitan_product_template
msgid "Cranberry Jus, lime jus, vodka and Cointreau"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Delete"
msgstr "Slet"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Delete Error"
msgstr "Slet fejl"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Design floors and assign orders to tables"
msgstr "Design gulv og tildel ordre til borde"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Dine in"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr "Drinks"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Early Receipt Printing"
msgstr "Tidlig kvitteringsudskrivning"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Eat in / Take out"
msgstr "Spis på stedet / Tag med"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.js:0
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Edit Order Name"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Edit Plan"
msgstr "Rediger Plan"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr "Aktivere deling af regning ved Point of Sale."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Encountered error when loading image. Please try again."
msgstr "Stødte på fejl ved indlæsning af billede. Forsøg venligst igen."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.js:0
msgid "Enter a table number"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.espresso_product_template
msgid "Espresso"
msgstr "Espresso"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_extra_cheese
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_extra_cheese_pasta
msgid "Extra cheese"
msgstr ""

#. module: pos_restaurant
#: model:product.attribute,name:pos_restaurant.product_extra_pasta
#: model:product.attribute,name:pos_restaurant.product_extra_pizza
msgid "Extras"
msgstr "Ekstra"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.fanta_product_template
msgid "Fanta"
msgstr "Fanta"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "File"
msgstr "Fil"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Bogføringsgruppe"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Fiscal Positions"
msgstr "Bogføringsgrupper"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr "Etage"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__floor_background_image
msgid "Floor Background Image"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr "Etage navn"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Floor Name ?"
msgstr "Etage navn ?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Floor Plan"
msgstr ""

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr "Grundplan"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Floor name"
msgstr "Etage navn"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid "Floor: %(floor)s - PoS Config: %(config)s \n"
msgstr "Etage: %(floor)s - PoS Konfiguration: %(config)s\n"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors"
msgstr "Restaurant etager"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Floors & Tables Map"
msgstr "Gulve og Borde kort"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr "Mad"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
msgid "For convenience, we are providing the following gratuity calculations:"
msgstr "For nemheds skyld, udbyder vi følgende drikkepenge beregninger:"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr "Svampe"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_negroni_product_template
msgid "Gin, vermouth rosso, Campari, and an orange peel."
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.green_tea_product_template
msgid "Green Tea"
msgstr "Grøn te"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_grilled_vegetables
msgid "Grilled vegetables"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
msgid "Guests"
msgstr "Gæster"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.js:0
msgid "Guests?"
msgstr "Gæster?"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "Højde"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr "Horisontal position"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "ID"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.ice_tea_product_template
msgid "Ice Tea"
msgstr "Is te"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr ""
"Hvis falsk, deaktiveres bordet og vil ikke være tilgængelig i point of sale"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.js:0
msgid "Jump"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen.xml:0
msgid "Keep Open"
msgstr "Hold Åben"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Loading"
msgstr "Indlæser"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Loading Image Error"
msgstr "Indlæser Billede Fejl"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr "Frokost Maki-ruller 18stk"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr "Frokost Laks 20stk"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr "Frokost Temaki mix 3stk"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_mai_tai_product_template
msgid "Mai Tai"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Make Round"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Make Square"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_margarita_product_template
msgid "Margarita"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Message"
msgstr "Besked"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.milkshake_banana_product_template
msgid "Milkshake Banana"
msgstr "Milkshake Banan"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr "Minute Maid"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_mojito_product_template
msgid "Mojito"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_moscow_mule_product_template
msgid "Moscow Mule"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr "Mozzarella Sandwich"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_mushroom
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_mushroom_pasta
msgid "Mushroom"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_negroni_product_template
msgid "Negroni"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "New Floor"
msgstr "Ny etage"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "No Tip"
msgstr "Ingen Drikkepenge"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Note"
msgstr "Notat"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Number of Seats?"
msgstr "Antal pladser?"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_old_fashioned_product_template
msgid "Old Fashioned"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Online reservation for restaurant"
msgstr "Online reservation til restaurant"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr ""
"Kun web-kompatible billedformater så som .png eller .jpeg understøttes."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Oops! No floors available."
msgstr "Ups! Ingen etager tilgængelige."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Oops! No tables available."
msgstr "Ups! Ingen borde tilgængelige."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
msgid "Open"
msgstr "Åben"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "Order"
msgstr "Ordre"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
msgid "PRO FORMA"
msgstr "PRO FORMA"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__parent_id
msgid "Parent Table"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 Formaggi"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr "Pasta bolognese"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/receipt_screen.xml:0
msgid "Pay"
msgstr "Betal"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/actionpad_widget/actionpad_widget.xml:0
msgid "Payment"
msgstr "Betaling"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pepe, latte, gorgonzola dolce, taleggio, parmigiano reggiano"
msgstr ""

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_peperoni
msgid "Pepperoni"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Picture"
msgstr "Billede"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_pina_colada_product_template
msgid "Pina colada"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Pizza Margherita"
msgstr "Pizza Margherita"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_vege_product_template
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Pizza Vegetarian"
msgstr "Pizza Vegetar"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Plan"
msgstr "Planlæg"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS konfiguration"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "POS ordrer"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Point of Sale betalinger"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_session
msgid "Point of Sale Session"
msgstr "PoS session"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_ids
msgid "Point of Sales"
msgstr "POS"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_printbill
msgid "Pos Iface Printbill"
msgstr "Pos Iface Udskrivregning"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_iface_splitbill
msgid "Pos Iface Splitbill"
msgstr "Pos Iface Splitregning"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_set_tip_after_payment
msgid "Pos Set Tip After Payment"
msgstr "Pos Angiv drikkepenge efter betaling"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_potato_thyme
msgid "Potatoes with thyme"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/bill_screen/bill_screen.xml:0
msgid "Print"
msgstr "Udskriv"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_aperol_spritz_product_template
msgid "Prosecco, aperol, soda"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/order_summary/order_summary.xml:0
msgid "Release Order"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/product_screen/order_summary/order_summary.xml:0
msgid "Release table"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Removing a floor cannot be undone. Do you still want to remove %s?"
msgstr "Fjernelse af et gulv kan ikke fortrydes. Vil du stadig fjerne %s?"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Removing a table cannot be undone"
msgstr "Ved flytning af bord kan man ikke fortryde"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Rename"
msgstr "Omdøb"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Reprint receipts"
msgstr "Genudskriv kvitteringer"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_config.py:0
msgid "Restaurant"
msgstr "Restaurant"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr "Restaurant etager"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "Restaurant etager"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr "Restaurant bord"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
msgid "Reverse"
msgstr "Tilbagefør"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/payment_screen/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
msgid "Reverse Payment"
msgstr "Tilbagebetal betaling"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr "Rund"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Round Shape"
msgstr "Rund form"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_mai_tai_product_template
msgid "Rum, lime juice, orgeat syrup, and orange liqueur."
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr "Laks og avocado"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Save"
msgstr "Gem"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "Gem denne side og kom tilbage hertil for at konfigurere funktionen."

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.schweppes_product_template
msgid "Schweppes"
msgstr "Schweppes"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
msgid "Seats"
msgstr "Sæder"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Select table to transfer order"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__set_tip_after_payment
msgid "Set Tip After Payment"
msgstr "Angiv Drikkepenge Efter Betaling"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
msgid "Settle"
msgstr "Afstem"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
msgid "Shape"
msgstr "Form"

#. module: pos_restaurant
#: model:product.attribute,name:pos_restaurant.product_sides_buns_pizza
msgid "Sides"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Signature"
msgstr "Signatur"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_value_smashed_sweet_potatoes
msgid "Smashed sweet potatoes"
msgstr ""

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.pos_category_soft_drinks
msgid "Soft drinks"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr "Stærk tun sandwich"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Split"
msgstr "Del"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/split_bill_screen/split_bill_screen.xml:0
msgid "Split Order"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Split total or order lines"
msgstr "Opdel total eller ordrelinjer"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr "Firkantet"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
msgid "Square Shape"
msgstr "Firkantet form"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Subtotal"
msgstr "Subtotal"

#. module: pos_restaurant
#: model:product.attribute.value,name:pos_restaurant.product_attribute_sweet_potato
msgid "Sweet potato fries"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Switch Floor View"
msgstr "Skift gulvvisning"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Switch to Dine in"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Switch to Takeaway"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
msgid "Table"
msgstr "Tabel"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant_appointment
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_module_pos_restaurant_appointment
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Table Booking"
msgstr "Bordbestilling"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__table_number
msgid "Table Number"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.js:0
#: code:addons/pos_restaurant/static/src/overrides/components/navbar/navbar.xml:0
msgid "Table Selector"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "Borde"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__takeaway
msgid "Take Away"
msgstr "Tag væk"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/models/pos_store.js:0
msgid "Take out"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__takeaway
#: model:ir.model.fields,field_description:pos_restaurant.field_res_config_settings__pos_takeaway
msgid "Takeaway"
msgstr "Tag væk"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.res_config_settings_view_form
msgid "Taxes must be included in price."
msgstr ""

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_margarita_product_template
msgid "Tequila Jose Cuervo, lime jus, sugar cane Cointreau"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr "Antal kunder som er blevet betjent på denne ordre."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid "The background color of the floor in a html-compatible format"
msgstr "Baggrundsfarven på gulvet i et html-kompatibelt format"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr "Standardantallet af kunder betjent ved dette bord."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__table_number
msgid "The number of the table as displayed on the floor plan"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__parent_id
msgid "The parent table if this table is part of a group of tables"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr "Restaurantområderne som betjenes i dennes kasse."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr "Bordet hvor denne ordre blev serveret"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr "Bordets farve, udtrykt som en gyldig CSS 'baggrunds' egenskabs værdi"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr "Bordets højde i pixels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr ""
"Bordets horisontale position fra venstre side til bordets midte, i pixels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr "Bordet vertikale position fra toppen til bordets midte, i pixels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr "Bordets bredde i pixels"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__takeaway_fp_id
#: model:ir.model.fields,help:pos_restaurant.field_res_config_settings__pos_takeaway_fp_id
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"Dette er nyttigt for restauranter med afhentning eller take-away tjenester "
"der indbefatter specifikke afgifter."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.js:0
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""
"Denne ordre er endnu ikke synkroniseret til serveren. Sørg for, at det er "
"synkroniseret, og prøv derefter igen."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.xml:0
msgid "Tip"
msgstr "Tip"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Tip Amount"
msgstr "Tip Beløb"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Tip:"
msgstr "Drikkepenge:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/ticket_screen/ticket_screen.js:0
msgid "Tipping"
msgstr "Drikkepenge"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.pos_food_margherita_product_template
msgid "Tomato sauce, Agerola mozzarella \"fior di latte\", fresh basil"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "Total:"
msgstr "Total:"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/control_buttons/control_buttons.xml:0
msgid "Transfer / Merge"
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "Unsupported File Format"
msgstr "Ikke-understøttet fil format"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.js:0
msgid "Unsynced order"
msgstr "Usynkroniseret ordre"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_screen/tip_screen.xml:0
msgid "Username"
msgstr "Brugernavn"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr "Vertical position"

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_moscow_mule_product_template
msgid "Vodka 42 Below, lime, sugar, ginger beer"
msgstr ""

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr "Vand"

#. module: pos_restaurant
#: model:product.template,name:pos_restaurant.product_whiskey_sour_product_template
msgid "Whiskey Sour"
msgstr ""

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_whiskey_sour_product_template
msgid "Whiskey, lemon juice, sugar, and a dash of egg white."
msgstr ""

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_pina_colada_product_template
msgid "White rhum, Malibu, Batida de coco, coconut liqueur, pineapple juice"
msgstr ""

#. module: pos_restaurant
#: model:product.template,description_sale:pos_restaurant.product_mojito_product_template
msgid "White rum, sugar, lime juice, soda water, and mint."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "Bredde"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot delete a floor when orders are still in draft for this floor."
msgstr ""
"Du kan ikke slette en etage, når ordrer stadig er i udkast til denne etage."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "You cannot delete a floor with orders still in draft for this floor."
msgstr ""

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot delete a table when orders are still in draft for this table."
msgstr ""

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/floor_screen/floor_screen.js:0
msgid "You cannot delete a table with orders still in draft for this table."
msgstr ""
"Du kan ikke slette et bord, når der stadig er ordrer i udkast til dette "
"bord."

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot remove a floor that is used in a PoS session, close the "
"session(s) first: \n"
msgstr ""
"Du kan ikke fjerne en etage der bruges i en PoS session, luk session(erne) "
"først: \n"

#. module: pos_restaurant
#. odoo-python
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
msgid ""
"You cannot remove a table that is used in a PoS session, close the "
"session(s) first."
msgstr ""
"Du kan ikke fjerne et bord der anvendes i en PoS session, afslut "
"session(erne) først."

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "________________________"
msgstr "________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/app/tip_receipt/tip_receipt.xml:0
msgid "______________________________________________"
msgstr "______________________________________________"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "changes"
msgstr "ændringer"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/product_screen/product_screen.xml:0
msgid "items"
msgstr "varer"

#. module: pos_restaurant
#. odoo-javascript
#: code:addons/pos_restaurant/static/src/overrides/components/order_widget/order_widget.js:0
msgid "or book the table for later"
msgstr "eller bestil bordet til senere"
