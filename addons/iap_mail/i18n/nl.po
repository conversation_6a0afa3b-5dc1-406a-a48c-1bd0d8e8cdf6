# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap_mail
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-building text-primary\"/>\n"
"                    <b>Company type</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-building text-primary\"/>\n"
"                    <b>Bedrijfstype</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-calendar text-primary\"/>\n"
"                    <b>Founded</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-calendar text-primary\"/>\n"
"                    <b>Opgericht</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-cube text-primary\"/>\n"
"                    <b>Technologies Used</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-cube text-primary\"/>\n"
"                    <b>Gebruikte technologieën</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-envelope text-primary\"/>\n"
"                    <b>Email</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-envelope text-primary\"/>\n"
"                    <b>E-mail</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-globe text-primary\"/>\n"
"                    <b>Timezone</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-globe text-primary\"/>\n"
"                    <b>Tijdzone</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-industry text-primary\"/>\n"
"                    <b>Sectors</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-industry text-primary\"/>\n"
"                    <b>Branches</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-money text-primary\"/>\n"
"                    <b>Estimated revenue</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-money text-primary\"/>\n"
"                    <b>Geschatte omzet</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-phone text-primary\"/>\n"
"                    <b>Phone</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-phone text-primary\"/>\n"
"                    <b>Telefoon</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-twitter text-primary\"/>\n"
"                    <b>X</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-twitter text-primary\"/>\n"
"                    <b>X</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-users text-primary\"/>\n"
"                    <b>Employees</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-users text-primary\"/>\n"
"                    <b>Werknemers</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "<span> per year</span>"
msgstr "<span> per jaar</span>"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_needaction
msgid "Action Needed"
msgstr "Actie gevraagd"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_attachment_count
msgid "Attachment Count"
msgstr "Aantal bijlagen"

#. module: iap_mail
#. odoo-javascript
#: code:addons/iap_mail/static/src/js/services/iap_notification_service.js:0
msgid "Buy more credits"
msgstr "Koop meer credits"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__company_ids
msgid "Company"
msgstr "Bedrijf"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__warning_user_ids
msgid "Email Alert Recipients"
msgstr "Ontvangers van e-mailwaarschuwingen"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__warning_threshold
msgid "Email Alert Threshold"
msgstr "Drempel e-mailwaarschuwing"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_follower_ids
msgid "Followers"
msgstr "Volgers"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_partner_ids
msgid "Followers (Partners)"
msgstr "Volgers (Partners)"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__has_message
msgid "Has Message"
msgstr "Heeft bericht"

#. module: iap_mail
#: model:ir.model,name:iap_mail.model_iap_account
msgid "IAP Account"
msgstr "IAP account"

#. module: iap_mail
#: model:ir.model.fields,help:iap_mail.field_iap_account__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Indien aangevinkt vragen nieuwe berichten je aandacht."

#. module: iap_mail
#: model:ir.model.fields,help:iap_mail.field_iap_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Indien aangevinkt hebben sommige berichten een leveringsfout."

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_is_follower
msgid "Is Follower"
msgstr "Is een volger"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_has_error
msgid "Message Delivery error"
msgstr "Bericht afleverfout"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_ids
msgid "Messages"
msgstr "Berichten"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_needaction_counter
msgid "Number of Actions"
msgstr "Aantal acties"

#. module: iap_mail
#: model:ir.model.fields,field_description:iap_mail.field_iap_account__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: iap_mail
#: model:ir.model.fields,help:iap_mail.field_iap_account__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Aantal berichten die actie vereisen"

#. module: iap_mail
#: model:ir.model.fields,help:iap_mail.field_iap_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Aantal berichten met leveringsfout"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "followers"
msgstr "volgers"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "http://www.twitter.com/"
msgstr "http://www.twitter.com/"
