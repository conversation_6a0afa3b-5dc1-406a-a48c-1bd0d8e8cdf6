# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_geolocalize
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON> Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_id
msgid "API"
msgstr "API"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/base_geocoder.py:0
msgid ""
"API key for GeoCoding (Places) required.\n"
"Visit https://developers.google.com/maps/documentation/geocoding/get-api-key for more information."
msgstr ""
"ต้องใช้คีย์ API สำหรับ GeoCoding (สถานที่)\n"
"เยี่ยมชม https://developers.google.com/maps/documentation/geocoding/get-api-key for more information."

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.res_config_settings_view_form
msgid "API:"
msgstr "API:"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Compute Localization"
msgstr "การประยุกต์ใช้ของการคำนวณ"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Compute based on address"
msgstr "การคำนวณตามที่อยู่"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/base_geocoder.py:0
msgid "Error with geolocation server: %s"
msgstr "ข้อผิดพลาดกับเซิร์ฟเวอร์ระบุตำแหน่งทางภูมิศาสตร์: %s"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_base_geocoder
msgid "Geo Coder"
msgstr "รหัสทางภูมิศาสตร์"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geo Location"
msgstr "ที่ตั้งทางภูมิศาสตร์"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_base_geo_provider
msgid "Geo Provider"
msgstr "ผู้ให้บริการทางภูมิศาสตร์"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geolocation"
msgstr "ตำแหน่งทางภูมิศาสตร์"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_partner__date_localization
#: model:ir.model.fields,field_description:base_geolocalize.field_res_users__date_localization
msgid "Geolocation Date"
msgstr "วันที่ระบุตำแหน่งทางภูมิศาสตร์"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_googlemap_key
msgid "Google Map API Key"
msgstr "Google Map API Key"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__id
msgid "ID"
msgstr "ไอดี"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.res_config_settings_view_form
msgid "Key:"
msgstr "Key:"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Lat :"
msgstr "Lat :"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Long:"
msgstr "Long:"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__name
msgid "Name"
msgstr "ชื่อ"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/res_partner.py:0
msgid "No match found for %(partner_names)s address(es)."
msgstr "ไม่พบรายการที่ตรงกันสำหรับที่อยู่ %(partner_names)s"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Partner Assignment"
msgstr "การมอบหมายพาร์ทเนอร์"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/base_geocoder.py:0
msgid "Provider %s is not implemented for geolocation service."
msgstr "ผู้ให้บริการ %s ไม่ได้ถูกนำมาใช้สำหรับบริการระบุตำแหน่ง"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Refresh"
msgstr "รีเฟรช"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Refresh Localization"
msgstr "รีเฟรชการประยุกต์ใช้"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__tech_name
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_techname
msgid "Technical Name"
msgstr "ชื่อทางเทคนิค"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/base_geocoder.py:0
msgid ""
"Unable to geolocate, received the error:\n"
"%s\n"
"\n"
"Google made this a paid feature.\n"
"You should first enable billing on your Google account.\n"
"Then, go to Developer Console, and enable the APIs:\n"
"Geocoding, Maps Static, Maps Javascript.\n"
msgstr ""
"ไม่สามารถระบุตำแหน่งทางภูมิศาสตร์ได้ เกิดข้อผิดพลาด:\n"
"%s\n"
"\n"
"Google ทำให้ฟีเจอร์นี้เป็นฟีเจอร์ที่ต้องชำระเงิน\n"
"คุณควรเปิดใช้งานการเรียกเก็บเงินในบัญชี Google ของคุณก่อน\n"
"จากนั้นไปที่คอนโซลสำหรับนักพัฒนาซอฟต์แวร์และเปิดใช้งาน API:\n"
"การระบุพิกัดทางภูมิศาสตร์, แผนที่แบบคงที่, แผนที่ Javascript\n"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Updated on:"
msgstr "อัปเดตเมื่อ:"

#. module: base_geolocalize
#: model:ir.model.fields,help:base_geolocalize.field_res_config_settings__geoloc_provider_googlemap_key
msgid ""
"Visit https://developers.google.com/maps/documentation/geocoding/get-api-key"
" for more information."
msgstr ""
"เยี่ยมชม https://developers.google.com/maps/documentation/geocoding/get-api-"
"key for more information."

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/res_partner.py:0
msgid "Warning"
msgstr "คำเตือน"
