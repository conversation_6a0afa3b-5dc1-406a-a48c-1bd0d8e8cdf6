<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
    xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
    xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2">
    <cbc:CustomizationID>urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0
    </cbc:CustomizationID>
    <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
    <cbc:ID>___ignore___</cbc:ID>
    <cbc:IssueDate>2017-01-01</cbc:IssueDate>
    <cbc:DueDate>2017-02-28</cbc:DueDate>
    <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
    <cbc:Note>test narration</cbc:Note>
    <cbc:DocumentCurrencyCode>USD</cbc:DocumentCurrencyCode>
    <cbc:BuyerReference>ref_partner_2</cbc:BuyerReference>
    <cac:OrderReference>
        <cbc:ID>___ignore___</cbc:ID>
    </cac:OrderReference>
    <cac:AdditionalDocumentReference>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:Attachment>
            <cbc:EmbeddedDocumentBinaryObject mimeCode="___ignore___" filename="___ignore___">
                ___ignore___
            </cbc:EmbeddedDocumentBinaryObject>
        </cbc:Attachment>
    </cac:AdditionalDocumentReference>
    <cac:AccountingSupplierParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0208">**********</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID>ref_partner_1</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>partner_1</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Chauss&#233;e de Namur 40</cbc:StreetName>
                <cbc:CityName>Ramillies</cbc:CityName>
                <cbc:PostalZone>1367</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>BE</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>BE**********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>partner_1</cbc:RegistrationName>
                <cbc:CompanyID>BE**********</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>partner_1</cbc:Name>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingSupplierParty>
    <cac:AccountingCustomerParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0208">**********</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID>ref_partner_2</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>partner_2</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Rue des Bourlottes 9</cbc:StreetName>
                <cbc:CityName>Ramillies</cbc:CityName>
                <cbc:PostalZone>1367</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>BE</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>BE**********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>partner_2</cbc:RegistrationName>
                <cbc:CompanyID>BE**********</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>partner_2</cbc:Name>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingCustomerParty>
    <cac:Delivery>
        <cac:DeliveryLocation>
            <cac:Address>
                <cbc:StreetName>Rue des Bourlottes 9</cbc:StreetName>
                <cbc:CityName>Ramillies</cbc:CityName>
                <cbc:PostalZone>1367</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>BE</cbc:IdentificationCode>
                </cac:Country>
            </cac:Address>
        </cac:DeliveryLocation>
    </cac:Delivery>
    <cac:PaymentMeans>
        <cbc:PaymentMeansCode name="credit transfer">30</cbc:PaymentMeansCode>
        <cbc:PaymentID>___ignore___</cbc:PaymentID>
        <cac:PayeeFinancialAccount>
            <cbc:ID>****************</cbc:ID>
        </cac:PayeeFinancialAccount>
    </cac:PaymentMeans>
    <cac:PaymentTerms>
        <cbc:Note>Payment terms: 30% Advance End of Following Month</cbc:Note>
    </cac:PaymentTerms>
    <cac:TaxTotal>
        <cbc:TaxAmount currencyID="USD">15.75</cbc:TaxAmount>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="USD">75.00</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="USD">15.75</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
    </cac:TaxTotal>
    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="USD">75.00</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="USD">75.00</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="USD">90.75</cbc:TaxInclusiveAmount>
        <cbc:PrepaidAmount currencyID="USD">0.00</cbc:PrepaidAmount>
        <cbc:PayableAmount currencyID="USD">90.75</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">1.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="USD">100.00</cbc:LineExtensionAmount>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="USD">100.0</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">-1.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="USD">-25.00</cbc:LineExtensionAmount>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="USD">25.0</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
</Invoice>
