# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_group
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid ") <span invisible=\"not send_email\">and send them an email</span>."
msgstr ""
") <span invisible=\"not send_email\">y envíele un correo electrónico</span>."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "<br/> You'll be notified as soon as some new content is posted."
msgstr "<br/> Se le notificará en cuanto se publique contenido nuevo."

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_list_subscribe
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                Hello,<br/><br/>\n"
"                You have requested to be subscribed to the mailing list <strong t-out=\"object.name or ''\"/>.\n"
"                <br/><br/>\n"
"                To confirm, please visit the following link: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"/></a></strong>\n"
"                <br/><br/>\n"
"                If this was a mistake or you did not requested this action, please ignore this message.\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                Hola,<br/><br/>\n"
"                Solicitó suscribirse a la lista de envíos <strong t-out=\"object.name or ''\"/>.\n"
"                <br/><br/>\n"
"                Para confirmar, haga clic en el siguiente enlace: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"/></a></strong>\n"
"                <br/><br/>\n"
"                Si esto fue un error, o no solicitó esta acción, ignore este mensaje.\n"
"            </div>\n"
"        "

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_list_unsubscribe
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                Hello,<br/><br/>\n"
"                You have requested to be unsubscribed to the mailing list <strong t-out=\"object.name or ''\"/>.\n"
"                <br/><br/>\n"
"                To confirm, please visit the following link: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"/></a></strong>.\n"
"                <br/><br/>\n"
"                If this was a mistake or you did not requested this action, please ignore this message.\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                Hola,<br/><br/>\n"
"                Solicitó cancelar su suscripción de la lista de envíos <strong t-out=\"object.name or ''\"/>.\n"
"                <br/><br/>\n"
"                Para confirmar, haga clic en el siguiente enlace: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"/></a></strong>.\n"
"                <br/><br/>\n"
"                Si este fue un error o no solicitó esta acción, ignore este mensaje.\n"
"            </div>\n"
"        "

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_guidelines
msgid ""
"<div>\n"
"                <p>Hello <t t-out=\"object.partner_id.name or ''\"/>,</p>\n"
"                <p>Please find below the guidelines of the <t t-out=\"object.mail_group_id.name\"/> mailing list.</p>\n"
"                <p><t t-out=\"object.mail_group_id.moderation_guidelines_msg or ''\"/></p>\n"
"            </div>\n"
"        "
msgstr ""
"<div>\n"
"                <p>Hola <t t-out=\"object.partner_id.name or ''\"/>,</p>\n"
"                <p>A continuación encontrará las reglas de la <t t-out=\"object.mail_group_id.name\"/> lista de envíos.</p>\n"
"                <p><t t-out=\"object.mail_group_id.moderation_guidelines_msg or ''\"/></p>\n"
"            </div>\n"
"        "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Fecha\" title=\"Fecha\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "<i class=\"fa fa-envelope-o me-1\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr ""
"<i class=\"fa fa-envelope-o me-1\" role=\"img\" aria-label=\"Seudónimo\" "
"title=\"Seudónimo\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_name
msgid "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr ""
"<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Seudónimo\" "
"title=\"Seudónimo\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"<i class=\"fa fa-fw fa-user\" role=\"img\" aria-label=\"Recipients\" "
"title=\"Recipients\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-user\" role=\"img\" aria-label=\"Destinatarios\" "
"title=\"Destinatarios\"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid ""
"<i class=\"oi oi-arrow-left\" role=\"img\" aria-label=\"Previous message\" "
"title=\"Previous message\"/>"
msgstr ""
"<i class=\"oi oi-arrow-left\" role=\"img\" aria-label=\"Mensaje anterior \" "
"title=\"Mensaje anterior \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid ""
"<i class=\"oi oi-arrow-right\" role=\"img\" aria-label=\"Next message\" "
"title=\"Next message\"/>"
msgstr ""
"<i class=\"oi oi-arrow-right\" role=\"img\" aria-label=\"Siguiente mensaje\""
" title=\"Siguiente mensaje \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Show attachments\""
" title=\"Show attachments\"/>"
msgstr ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Mostrar archivos "
"adjuntos \" title=\"Mostrar archivos adjuntos \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Show replies\" "
"title=\"Show replies\"/>"
msgstr ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Mostrar "
"respuestas\" title=\"Mostrar respuestas \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Hide "
"attachments\" title=\"Hide attachments\"/>"
msgstr ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Ocultar archivos "
"adjuntos \" title=\"Ocultar archivos adjuntos \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Hide replies\" "
"title=\"Hide replies\"/>"
msgstr ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Ocultar "
"respuestas\" title=\"Ocultar respuestas \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "<span class=\"bg-warning\">Pending</span>"
msgstr "<span class=\"bg-warning\">Pendiente</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid ""
"<span class=\"ms-2 badge text-bg-success\" invisible=\"author_moderation != 'allow'\">Whitelisted</span>\n"
"                            <span class=\"ms-2 badge text-bg-danger\" invisible=\"author_moderation != 'ban'\">Banned</span>"
msgstr ""
"<span class=\"ms-2 badge text-bg-success\" invisible=\"author_moderation != 'allow'\">En lista de aprobación</span>\n"
"                            <span class=\"ms-2 badge text-bg-danger\" invisible=\"author_moderation != 'ban'\">Suspendido</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "<span class=\"mx-2\">-</span>"
msgstr "<span class=\"mx-2\">-</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "<span>By thread</span>"
msgstr "<span>Por conversación</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"<span>No Mail Group yet.</span>\n"
"                    <br/>"
msgstr ""
"<span>Todavía no hay grupo de correo.</span>\n"
"                    <br/>"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Un diccionario de Python que se evaluará con el fin de proporcionar valores "
"predeterminados cuando se creen nuevos registros para este seudónimo."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Accept"
msgstr "Aceptar"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__accepted
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Accepted"
msgstr "Aceptada"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__action
msgid "Action"
msgstr "Acción"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__active
msgid "Active"
msgstr "Activo"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid ""
"Add this email address to white list of people and accept all pending "
"messages from the same author."
msgstr ""
"Agregue esta dirección de correo a la lista de aprobación y acepte todos los"
" mensajes pendientes del mismo autor."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_id
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
msgid "Alias"
msgstr "Seudónimo"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_contact
msgid "Alias Contact Security"
msgstr "Seudónimo del contacto de seguridad"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_domain_id
msgid "Alias Domain"
msgstr "Dominio del seudónimo"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_domain
msgid "Alias Domain Name"
msgstr "Nombre del dominio del seudónimo"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_full_name
msgid "Alias Email"
msgstr "Correo electrónico del seudónimo"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_name
msgid "Alias Name"
msgstr "Nombre del seudónimo"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_status
msgid "Alias Status"
msgstr "Estado del seudónimo"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Estado del seudónimo evaluado en el último mensaje recibido."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_model_id
msgid "Aliased Model"
msgstr "Modelo con seudónimo"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "All messages of this group"
msgstr "Todos los mensajes de este grupo"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Allowed Emails"
msgstr "Correos electrónicos permitidos"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Alone we can do so little, together we can do so much"
msgstr ""
"No es mucho lo que podemos hacer solos, pero juntos podemos hacer mucho más."

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_moderation__status__allow
msgid "Always Allow"
msgstr "Siempre permitir"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
msgid "An email with instructions has been sent."
msgstr "Se envió un correo con las instrucciones."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Archived"
msgstr "Archivado"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "Archives"
msgstr "Archivados"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__attachment_ids
msgid "Attachments"
msgstr "Archivos adjuntos"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__author_id
msgid "Author"
msgstr "Autor"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__author_moderation
msgid "Author Moderation Status"
msgstr "Estado de moderación del autor"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Autor del mensaje. Si no se establece, email_from puede contener una "
"dirección de correo que no coincida con la de ningún contacto."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__access_group_id
msgid "Authorized Group"
msgstr "Grupo autorizado"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_notify
msgid "Automatic notification"
msgstr "Notificación automática"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message_reject__action__ban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Ban"
msgstr "Prohibición"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Ban the author of the message ("
msgstr "Suspenda al autor del mensaje ("

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid ""
"Ban this email address and reject all pending messages from the same author "
"and send an email to the author"
msgstr ""
"Suspenda esta dirección de correo y rechace todos los mensajes pendientes "
"del mismo autor y envíe un correo al autor"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__author_moderation__ban
msgid "Banned"
msgstr "Suspendido"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Banned Emails"
msgstr "Correos electrónicos suspendidos"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "By date"
msgstr "Por fecha"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__can_manage_group
msgid "Can Manage"
msgstr "Puede moderar"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__can_manage_group
msgid "Can manage the members"
msgstr "Puede moderar a los miembros"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__group_message_child_ids
msgid "Children"
msgstr "Secundario"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Choose or configure a custom domain"
msgstr "Elija o configure un dominio personalizado"

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_list_subscribe
msgid "Confirm subscription to {{ object.name }}"
msgstr "Confirmar suscripción a {{ object.name }}"

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_list_unsubscribe
msgid "Confirm unsubscription to {{ object.name }}"
msgstr "Confirmar la cancelación de su suscripción a {{ object.name }}"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__body
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__body
msgid "Contents"
msgstr "Contenidos"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_action
msgid "Create a Mail Group"
msgstr "Crear un grupo de correo"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Create a new group"
msgstr "Crear un grupo nuevo"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__create_date
msgid "Created on"
msgstr "Creado el"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__is_moderator
msgid "Current user is a moderator of the group"
msgstr "El usuario actual es el moderador del grupo"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Mensaje personalizado en caso de devolución"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_defaults
msgid "Default Values"
msgstr "Valores predeterminados"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__description
msgid "Description"
msgstr "Descripción"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Discard"
msgstr "Descartar"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__email
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__email
msgid "Email"
msgstr "Correo electrónico"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Email %s is invalid"
msgstr "El correo %s no es válido"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_email
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Email Alias"
msgstr "Seudónimo de correo electrónico"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__email_from_normalized
msgid "Email From"
msgstr "Correo electrónico de"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Dirección de correo del remitente. Este campo se establece cuando no se "
"encuentra ningún contacto que coincida y reemplaza el campo author_id en el "
"chatter."

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""
"Dominio del correo electrónico, por ejemplo, \"ejemplo.com\" en "
"\"<EMAIL>\""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Emails"
msgstr "Correos electrónicos"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Emails waiting an action for this group"
msgstr "Los correos que esperan una acción de este grupo"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__public
msgid "Everyone"
msgstr "Todos"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid "Follow-Ups"
msgstr "Seguimientos"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__email_from
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "From"
msgstr "Desde"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__mail_group_id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__mail_group_id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__mail_group_id
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Group"
msgstr "Grupo"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Group Message"
msgstr "Mensaje grupal"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Group Name"
msgstr "Nombre del grupo"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
msgid "Group message can only be linked to mail group. Current model is %s."
msgstr ""
"El mensaje grupal solo se puede vincular al grupo de correo. El modelo "
"actual es %s."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_guidelines_msg
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Guidelines"
msgstr "Reglas"

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_guidelines
msgid "Guidelines of group {{ object.mail_group_id.name }}"
msgstr "Reglas del grupo {{ object.mail_group_id.name }}"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Hello"
msgstr "Hola"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__id
msgid "ID"
msgstr "ID"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID del registro principal que tiene el seudónimo (ejemplo: el proyecto que "
"contiene el seudónimo para la creación de tareas)"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Si se establece, este contenido se enviará en automático a los usuarios no "
"autorizados en vez del mensaje predeterminado."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__image_128
msgid "Image"
msgstr "Imagen"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Invalid action for URL generation (%s)"
msgstr "Acción no válida para generar una URL (%s)"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_moderation.py:0
msgid "Invalid email address “%s”"
msgstr "La dirección de correo electrónico \"%s\" no es válida"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.invalid_token_subscription
msgid "Invalid or expired confirmation link."
msgstr "Enlace de confirmación sin validez o vencido."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Is Allowed"
msgstr "Está permitido"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Is Banned"
msgstr "Está suspendido"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__is_group_moderated
msgid "Is Group Moderated"
msgstr "Es un grupo moderado"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__is_member
msgid "Is Member"
msgstr "Es un miembro"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Join"
msgstr "Unirse"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Leave"
msgstr "Abandonar"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_member_action
msgid "Let people subscribe to your list online or manually add them here."
msgstr ""
"Permita que la gente se suscriba a su lista en línea, también puede "
"agregarles aquí de forma manual."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Detección entrante basada en la parte local"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Mail Group"
msgstr "Grupo de correo "

#. module: mail_group
#: model:res.groups,name:mail_group.group_mail_group_manager
msgid "Mail Group Administrator"
msgstr "Administrador del grupo de correo"

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_list_subscribe
msgid "Mail Group: Mailing List Subscription"
msgstr "Grupo de correo: suscripción a la lista de correos"

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_list_unsubscribe
msgid "Mail Group: Mailing List Unsubscription"
msgstr "Grupo de correos: cancelar la suscripción a la lista de correos"

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_guidelines
msgid "Mail Group: Send Guidelines"
msgstr "Grupo de correo: enviar reglas"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_action
#: model:ir.ui.menu,name:mail_group.mail_group_menu
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Mail Groups"
msgstr "Grupos de correo"

#. module: mail_group
#: model:ir.actions.server,name:mail_group.ir_cron_mail_notify_group_moderators_ir_actions_server
msgid "Mail List: Notify group moderators"
msgstr "Lista de correo: notifique a los moderadores del grupo"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__mail_message_id
msgid "Mail Message"
msgstr "Mensaje de correo"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_member
msgid "Mailing List Member"
msgstr "Miembro de la lista de correo"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_message
msgid "Mailing List Message"
msgstr "Mensaje de la lista de correo"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_moderation
msgid "Mailing List black/white list"
msgstr "Lista de correos aprobados y excluídos"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.portal_breadcrumbs_group
msgid "Mailing Lists"
msgstr "Listas de correo"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_action
msgid ""
"Mailing groups are communities that like to discuss a specific topic "
"together."
msgstr ""
"Los grupos de correo son comunidades a las que les gusta conversar sobre "
"temas específicos con otras personas."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Mailing-List:"
msgstr "Lista de correo:"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Member"
msgstr "Miembro"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_member_action
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_ids
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
msgid "Members"
msgstr "Miembros"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_count
msgid "Members Count"
msgstr "Número de miembros"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Members of this group"
msgstr "Miembros de este grupo"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__members
msgid "Members only"
msgstr "Solo miembros"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__mail_group_message_id
msgid "Message"
msgstr "Mensaje"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_message_reject_action
msgid "Message Rejection Explanation"
msgstr "Motivo de rechazo del mensaje"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_message_action
msgid "Messages"
msgstr "Mensajes"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_count
msgid "Messages Count"
msgstr "Número de mensajes"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_last_month_count
msgid "Messages Per Month"
msgstr "Mensajes por mes"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Messages are pending moderation"
msgstr "Hay mensajes pendientes de moderación"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__mail_group_message_moderation_count
msgid "Messages that need an action"
msgstr "Mensajes que requieren una acción"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Moderate Messages"
msgstr "Mensajes moderados"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation
msgid "Moderate this group"
msgstr "Moderar este grupo"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Moderated"
msgstr "Moderado"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__moderator_id
msgid "Moderated By"
msgstr "Moderado por"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_rule_ids
msgid "Moderated Emails"
msgstr "Correos electrónicos moderados"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_rule_count
msgid "Moderated emails count"
msgstr "Número de correos electrónicos moderados"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderated emails in this group"
msgstr "Correos moderados en este grupo"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Moderated group must have moderators."
msgstr "El grupo moderado debe tener moderadores."

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_moderation_action
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Moderation"
msgstr "Moderación"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_tree
msgid "Moderation Lists"
msgstr "Listas de moderación"

#. module: mail_group
#: model:ir.ui.menu,name:mail_group.mail_group_moderation_menu
msgid "Moderation Rules"
msgstr "Reglas de moderación"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderations"
msgstr "Moderaciones"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__is_moderator
msgid "Moderator"
msgstr "Moderador"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderator_ids
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderators"
msgstr "Moderadores"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Moderators must have an email address."
msgstr "Los moderadores deben tener una dirección de correo electrónico."

#. module: mail_group
#: model:mail.group,name:mail_group.mail_group_1
msgid "My Company News"
msgstr "Noticias de Mi Empresa"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__name
msgid "Name"
msgstr "Nombre"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"Need to unsubscribe? <br/>It's right here! <span class=\"oi fa-2x oi-arrow-"
"down float-end\" role=\"img\" aria-label=\"\" title=\"Read this !\"/>"
msgstr ""
"¿Necesita cancelar su suscripción? <br/>¡Aquí lo puede hacer! <span "
"class=\"oi fa-2x oi-arrow-down float-end\" role=\"img\" aria-label=\"\" "
"title=\"¡Lea esto!\"/>"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__moderation_guidelines
msgid ""
"Newcomers on this moderated group will automatically receive the guidelines."
msgstr ""
"Los recién agregados a este grupo moderado recibirán las reglas en "
"automático."

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_member_action
msgid "No Members in this list yet!"
msgstr "¡Todavía no hay miembros en esta lista!"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_message_action
msgid "No Messages in this list yet!"
msgstr "¡Todavía no hay mensajes en esta lista!"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__email_normalized
msgid "Normalized Email"
msgstr "Correo electrónico normalizado"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__email_from_normalized
msgid "Normalized From"
msgstr "Normalizado de"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_notify_msg
msgid "Notification message"
msgstr "Mensaje de notificación"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Notify Members"
msgstr "Notificar a los miembros"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__mail_group_message_count
msgid "Number of message in this group"
msgstr "Número de mensajes en este grupo"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid ""
"Only an administrator or a moderator can send guidelines to group members."
msgstr ""
"Solo un administrador o un moderador puede enviar las reglas a los miembros "
"del grupo."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Only members can send email to the mailing list."
msgstr "Solo los miembros pueden enviar mensajes a esta lista de correo."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "Only selected groups of users can send email to the mailing list."
msgstr ""
"Solo los grupos de usuarios seleccionados pueden enviar mensajes a la lista "
"de correos. "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID opcional de un hilo (registro) al que se adjuntarán todos los mensajes "
"entrantes, incluso si no fueron respuestas del mismo. Si se establece, se "
"deshabilitará por completo la creación de nuevos registros."

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__group_message_parent_id
msgid "Parent"
msgstr "Principal"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_parent_model_id
msgid "Parent Model"
msgstr "Modelo principal"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID del hilo del registro principal"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Modelo principal que posee el seudónimo. El modelo que contiene la "
"referencia del seudónimo no es necesariamente el modelo dado por "
"alias_model_id (por ejemplo: proyecto (parent_model) y tarea (model))"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_partner_ids
msgid "Partners Member"
msgstr "Contactos que son miembros"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_ids
msgid "Pending Messages"
msgstr "Mensajes pendientes"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_moderation_count
msgid "Pending Messages Count"
msgstr "Número de mensajes pendientes"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__pending_moderation
msgid "Pending Moderation"
msgstr "Moderación pendiente"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__moderation_notify
msgid ""
"People receive an automatic notification about their message being waiting "
"for moderation."
msgstr ""
"Los usuarios reciben una notificación automática que les indica que su "
"mensaje está en espera de moderación."

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_moderation__status__ban
msgid "Permanent Ban"
msgstr "Suspensión permanente"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"La política sobre cómo publicar un mensaje en el documento con el servidor de correo.\n"
"- Todos: todos pueden publicar\n"
"- Contactos: solo los contactos verificados\n"
"- Seguidores: solo los seguidores del documento relacionado o los miembros de los canales\n"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Post to:"
msgstr "Publicar en:"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__create_date
msgid "Posted"
msgstr "Registrado"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__access_mode
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Privacy"
msgstr "Privacidad"

#. module: mail_group
#: model:mail.group,name:mail_group.mail_group_2
msgid "Public Mailing List"
msgstr "Lista de correo pública"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/wizard/mail_group_message_reject.py:0
msgid "Re: %s"
msgstr "Asunto: %s"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID del hilo de registro"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid "Reference"
msgstr "Referencia"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message_reject__action__reject
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Reject"
msgstr "Rechazar"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_message_reject
msgid "Reject Group Message"
msgstr "Rechazar mensaje grupal"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Reject Silently"
msgstr "Rechazar sin notificar"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Reject the message"
msgstr "Rechazar el mensaje"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__rejected
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Rejected"
msgstr "Rechazado"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Remove message with explanation"
msgstr "Eliminar mensaje y explicar el motivo"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Responsible Users"
msgstr "Usuarios responsables"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
msgid "Search Group Message"
msgstr "Buscar mensaje grupal"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_member_view_search
msgid "Search Mail Group Member"
msgstr "Buscar miembro del grupo de correo"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Search Mail group"
msgstr "Buscar grupo de correo"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Search Moderation List"
msgstr "Buscar en la lista de moderación"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__groups
msgid "Selected group of users"
msgstr "Grupo de usuarios seleccionado"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Send"
msgstr "Enviar"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Send & Ban"
msgstr "Enviar y suspender"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Send & Reject"
msgstr "Enviar y rechazar"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__send_email
msgid "Send Email"
msgstr "Enviar correo electrónico"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Send Guidelines"
msgstr "Enviar reglas"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message_reject__send_email
msgid "Send an email to the author of the message"
msgstr "Enviar un correo al autor del mensaje"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_guidelines
msgid "Send guidelines to new subscribers"
msgstr "Enviar reglas a los nuevos suscriptores "

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_guidelines
msgid "Sent to people who subscribed to a mailing group with group guidelines"
msgstr ""
"Se le envía a personas que cancelaron su suscripción a un grupo de correos "
"con reglas de grupo"

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_list_unsubscribe
msgid "Sent to people who unsubscribed from a mailing group"
msgstr ""
"Se le envía a personas que cancelaron su suscripción de un grupo de correo"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__moderation_status
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__status
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Status"
msgstr "Estado"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Stay in touch with our Community"
msgstr "Permanecer en contacto con nuestra comunidad"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__subject
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__subject
msgid "Subject"
msgstr "Asunto"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Subscribe"
msgstr "Suscribirse"

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_list_subscribe
msgid "Subscription confirmation to a mailing group"
msgstr "Confirmación de suscripción a un grupo de correo"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid ""
"Template \"mail_group.mail_template_guidelines\" was not found. No email has"
" been sent. Please contact an administrator to fix this issue."
msgstr ""
"No se encontró la plantilla \"mail_group.mail_template_guidelines\". No se "
"envió ningún correo, contacte a su administrador para solucionar este "
"problema."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Thank you!"
msgstr "¡Gracias!"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "The \"Authorized Group\" is missing."
msgstr "Falta el \"grupo autorizado\"."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "The email"
msgstr "El correo"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
msgid "The email \"%s\" is not valid."
msgstr "El correo \"%s\" no es válido."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "The group of the message do not match."
msgstr "El grupo del mensaje no coincide."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "The guidelines description is empty."
msgstr "La descripción de las reglas está vacía."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "The guidelines description is missing."
msgstr "Falta la descripción de las reglas."

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"El modelo (tipo de documento de Odoo) al que corresponde este seudónimo. "
"Cualquier correo entrante que no sea respuesta a un registro existente "
"creará un nuevo registro de este modelo (por ejemplo, una tarea de "
"proyecto)."

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"El nombre del seudónimo de correo electrónico. Por ejemplo, \"trabajos\" si "
"desea recibir correos electrónicos en <<EMAIL>>."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "The notification message is missing."
msgstr "Falta el mensaje de notificación."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
msgid "The partner can not be found."
msgstr "No se puede encontrar el contacto."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
msgid "The record of the message should be the group."
msgstr "El registro del mensaje debería ser el grupo."

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
msgid "This email is already subscribed."
msgstr "Este correo ya está suscrito."

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
msgid "This email is not subscribed."
msgstr "Este correo no está suscrito."

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
msgid "This message can not be moderated"
msgstr "Este mensaje no se puede moderar"

#. module: mail_group
#: model:ir.model.constraint,message:mail_group.constraint_mail_group_member_unique_partner
msgid "This partner is already subscribed to the group"
msgstr "Este contacto ya está suscrito al grupo"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
msgid "Those messages can not be moderated: %s."
msgstr "Esos mensajes no se pueden moderar: %s."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "To Review"
msgstr "Por revisar"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Unsubscribe"
msgstr "Cancelar suscripción"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Unsubscribe:"
msgstr "Cancelar suscripción:"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_message_action
msgid ""
"When people send an email to the alias of the list, they will appear here."
msgstr ""
"Aquí aparecerán los correos que la gente envíe al seudónimo de esta lista."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Whitelist"
msgstr "Lista de aprobación"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__author_moderation__allow
msgid "Whitelisted"
msgstr "En la lista de aprobación"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
msgid "Wrong status (%s)"
msgstr "Estado equivocado (%s)"

#. module: mail_group
#: model:ir.model.constraint,message:mail_group.constraint_mail_group_moderation_mail_group_email_uniq
msgid "You can create only one rule for a given email address in a group."
msgstr ""
"Solo puede crear una regla para una dirección de correo determinada en un "
"grupo."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "You have messages to moderate, please go for the proceedings."
msgstr "Tiene mensajes que moderar, siga los procedimientos."

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "_______________________________________________"
msgstr "_______________________________________________"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "alias"
msgstr "seudónimo"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "and send an email to the author ("
msgstr "y enviar un correo al autor ("

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid "attachments"
msgstr "archivos adjuntos"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "by"
msgstr "por"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "e.g. \"Newsletter\""
msgstr "Por ejemplo, \"Boletín\""

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "e.g. mycompany.com"
msgstr "Por ejemplo, miempresa.com"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
msgid "group"
msgstr "grupo"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "has been"
msgstr "se"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"members<br/>\n"
"                        <i class=\"fa fa-fw fa-envelope-o\" role=\"img\" aria-label=\"Traffic\" title=\"Traffic\"/>"
msgstr ""
"miembros<br/>\n"
"                        <i class=\"fa fa-fw fa-envelope-o\" role=\"img\" aria-label=\"Tráfico\" title=\"Tráfico \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "messages / month"
msgstr "mensajes / mes"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "more replies"
msgstr "más respuestas"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "replies"
msgstr "respuestas"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "subscribed to"
msgstr "suscrito a"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "the list"
msgstr "la lista"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "unsubscribed from"
msgstr "canceló la suscripción a"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "your email..."
msgstr "su correo electrónico..."
