# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_stock_account
# 
# Translators:
# Wil Odoo, 2024
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "3D printing pen"
msgstr "3D-printpen"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "447 out of 1,856"
msgstr "447 van 1.856"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "76,437 out of 188,071"
msgstr "76.437 van 188.071"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Ageing stock qty by category and creation date"
msgstr "Verouderde voorraad per categorie en aanmaakdatum"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Ageing stock value by product and creation date"
msgstr "Ouderdom voorraadwaarde per product en aanmaakdatum"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available Quantity"
msgstr "Beschikbare aantal"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available Value"
msgstr "Beschikbare waarde"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock qty (top locations)"
msgstr "Beschikbare en gereserveerde voorraad (toplocaties)"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock qty (top propducts)"
msgstr "Beschikbare en gereserveerde voorraad (top producten)"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock value (top locations)"
msgstr "Beschikbare en gereserveerde voorraadwaarde (toplocaties)"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Available and reserved stock value (top propducts)"
msgstr "Beschikbare en gereserveerde voorraadwaarde (topproducten)"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Bluetooth-enabled LED light strip"
msgstr "LED-lichtlijst met Bluetooth"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Compact espresso machine"
msgstr "Compacte espressomachine"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Count of products with negative stock"
msgstr "Aantal producten met negatieve voorraad"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Electric standing desk"
msgstr "Elektrisch sta-bureau"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Ergonomic office chair"
msgstr "Ergonomische bureaustoel"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "KPI"
msgstr "KPI"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Lines with negative stock"
msgstr "Regels met negatieve voorraad"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Location"
msgstr "Locatie"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Lot/Serial"
msgstr "Partij/Serienummer"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Post-production"
msgstr "Post-productie"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Pre-production"
msgstr "Pre-productie"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Product"
msgstr "Product"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Product Category"
msgstr "Productcategorie"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Products"
msgstr "Producten"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Reserved"
msgstr "Gereserveerd"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Reserved Quantity"
msgstr "Gereserveerde aantal"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Reserved Value"
msgstr "Gereserveerde waarde"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share of reserved stock Value"
msgstr "Aandeel gereserveerde voorraadwaarde"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share of reserved stock qty"
msgstr "Aandeel gereserveerde voorraad"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share reserved stock Qty"
msgstr "Aandeel gereserveerde voorraad"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Share reserved stock Value"
msgstr "Aandeel gereserveerde voorraadwaarde"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Smart air purifier"
msgstr "Slimme luchtreiniger"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Solar-powered phone charger"
msgstr "Telefoonoplader op zonne-energie"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Top 10 products with negative stock"
msgstr "Top 10 producten met negatieve voorraad"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Total"
msgstr "Totaal"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Total inventory value"
msgstr "Totale voorraadwaarde"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "WH/Output"
msgstr "WH/Uitgaand"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "WH/Stock"
msgstr "WH/Voorraad"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "WH/Stock/Shelf 10"
msgstr "WH/voorraad/plank 10"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_dashboard.json:0
msgid "Warehouse"
msgstr "Magazijn"

#. module: spreadsheet_dashboard_stock_account
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_stock_account.spreadsheet_dashboard_warehouse_metrics
msgid "Warehouse Metrics"
msgstr "Magazijnstatistieken"

#. module: spreadsheet_dashboard_stock_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_stock_account/data/files/warehouse_metrics_sample_dashboard.json:0
msgid "Waterproof hiking backpack"
msgstr "Waterdichte wandelrugzak"
