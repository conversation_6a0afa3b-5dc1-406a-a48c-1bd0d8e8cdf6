# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* resource
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:48+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
#: code:addons/resource/models/resource_resource.py:0
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "<span class=\"o_stat_text\">Time Off</span>"
msgstr "<span class=\"o_stat_text\">Congedo</span>"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "<span class=\"o_stat_text\">Work Resources</span>"
msgstr "<span class=\"o_stat_text\">Risorse lavoro</span>"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "<span> hours/week</span>"
msgstr "<span> ore/settimana</span>"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__active
#: model:ir.model.fields,field_description:resource.field_resource_resource__active
msgid "Active"
msgstr "Attivo"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__afternoon
msgid "Afternoon"
msgstr "Pomeriggio"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Archived"
msgstr "In archivio"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch to a 1-week calendar? All work entries will "
"be lost."
msgstr ""
"Sei sicuro di passare al calendario di una settimana? Tutte le voci "
"lavorative verranno perse."

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch to a 2-week calendar? All work entries will "
"be lost."
msgstr ""
"Sei sicuro di passare al calendario di due settimane? Tutte le voci "
"lavorative verranno perse."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Attendances can't overlap."
msgstr "Le presenze non possono sovrapporsi."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__hours_per_day
msgid "Average Hour per Day"
msgstr "Ore giornaliere medie"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__hours_per_day
msgid ""
"Average hours per day a resource is supposed to work with this calendar."
msgstr ""
"Ore lavorative giornaliere medie ipotizzate per la risorsa in base a questo "
"calendario."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__lunch
msgid "Break"
msgstr "Pausa"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_calendar
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__two_weeks_calendar
msgid "Calendar in 2 weeks mode"
msgstr "Calendario in modalità 2 settimane"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_closing_days
msgid "Closing Days"
msgstr "Giorni di chiusura"

#. module: resource
#: model:ir.model,name:resource.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__company_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__company_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__company_id
#: model:ir.model.fields,field_description:resource.field_resource_resource__company_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Company"
msgstr "Azienda"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__full_time_required_hours
msgid "Company Full Time"
msgstr "Tempo pieno azienda"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Date"
msgstr "Data"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__day_period
msgid "Day Period"
msgstr "Fase del giorno"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__dayofweek
msgid "Day of Week"
msgstr "Giorno settimanale"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_res_users__resource_calendar_id
msgid "Default Working Hours"
msgstr "Orario lavorativo predefinito"

#. module: resource
#: model:ir.model.fields,help:resource.field_res_users__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_mixin__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_resource__calendar_id
msgid ""
"Define the working schedule of the resource. If not set, the resource will "
"have fully flexible working hours."
msgstr ""
"Definisci l'orario di lavoro di una risorsa. Se non configurato, la risorsa "
"avrà un orario completamente flessibile."

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_calendar_form
msgid ""
"Define working hours and time table that could be scheduled to your project "
"members"
msgstr ""
"Definisce le ore di lavoro e l'agenda che potrebbero essere programmati dai "
"vostri membri del team"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__display_name
#: model:ir.model.fields,field_description:resource.field_resource_resource__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_type
msgid "Display Type"
msgstr "Tipo di visualizzazione"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__duration_days
msgid "Duration (days)"
msgstr "Durata (giorni)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__duration_hours
msgid "Duration (hours)"
msgstr "Durata (ore)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__time_efficiency
msgid "Efficiency Factor"
msgstr "Fattore di efficienza"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__email
msgid "Email"
msgstr "E-mail"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_to
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_to
msgid "End Date"
msgstr "Data fine"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_explanation
msgid "Explanation"
msgstr "Spiegazione"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__share
msgid ""
"External user with limited access, created only for the purpose of sharing "
"data."
msgstr ""
"Utente esterno con limitato accesso, creato solamente con lo scopo di "
"condividere dati."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__0
msgid "First"
msgstr "Primo/a"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "First week"
msgstr "Prima settimana"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__flexible_hours
msgid "Flexible Hours"
msgstr "Orario flessibile"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__4
msgid "Friday"
msgstr "Venerdì"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Friday Afternoon"
msgstr "Venerdì pomeriggio"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Friday Lunch"
msgstr "Pranzo del venerdì"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Friday Morning"
msgstr "Venerdì mattina"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
msgid "Fully Flexible"
msgstr "Completamente flessibile"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__sequence
msgid "Gives the sequence of this line when displaying the resource calendar."
msgstr ""
"Dà la sequenza di questa linea quando si visualizza il calendario delle "
"risorse."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__global_leave_ids
msgid "Global Time Off"
msgstr "Ferie globali"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
msgid "Hours"
msgstr "Ore"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Hours per Week"
msgstr "Ore alla settimana"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__user
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Human"
msgstr "Umano"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__id
#: model:ir.model.fields,field_description:resource.field_resource_resource__id
msgid "ID"
msgstr "ID"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__resource_id
msgid ""
"If empty, this is a generic time off for the company. If a resource is set, "
"the time off is only for this resource"
msgstr ""
"Se vuoto, sono ferie generiche per l'azienda. Se viene impostata una "
"risorsa, le ferie sono solo relative alla risorsa"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Se il campo Attivo è impostato a falso, consente di nascondere il record "
"della risorsa senza rimuoverlo."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__active
msgid ""
"If the active field is set to false, it will allow you to hide the Working "
"Time without removing it."
msgstr ""
"Se il campo Attivo è impostato a falso, consente di nascondere l'orario "
"lavorativo senza rimuoverlo."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid ""
"In a calendar with 2 weeks mode, all periods need to be in the sections."
msgstr ""
"In un calendario con modalità 2 settimane, tutti i periodi devono essere "
"nelle sezioni."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__material
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Material"
msgstr "Materiale"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__0
msgid "Monday"
msgstr "Lunedì"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Monday Afternoon"
msgstr "Lunedì pomeriggio"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Monday Lunch"
msgstr "Pranzo del lunedì"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Monday Morning"
msgstr "Lunedì mattina"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__morning
msgid "Morning"
msgstr "Mattino"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__name
#: model:ir.model.fields,field_description:resource.field_resource_resource__name
msgid "Name"
msgstr "Nome"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__full_time_required_hours
msgid ""
"Number of hours to work on the company schedule to be considered as "
"fulltime."
msgstr ""
"Numero di ore di lavoro secondo l'orario dell'azienda da considerare come "
"tempo pieno"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__other
msgid "Other"
msgstr "Altro"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Period"
msgstr "Periodo"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__phone
msgid "Phone"
msgstr "Telefono"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__name
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Reason"
msgstr "Motivo"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Nome utente correlato per gestire l'accesso alla risorsa."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_id
#: model:ir.ui.menu,name:resource.menu_resource_config
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Resource"
msgstr "Risorsa"

#. module: resource
#: model:ir.model,name:resource.model_resource_mixin
msgid "Resource Mixin"
msgstr "Mixin risorsa"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_leave_tree
#: model:ir.actions.act_window,name:resource.resource_calendar_leaves_action_from_calendar
#: model:ir.ui.menu,name:resource.menu_view_resource_calendar_leaves_search
msgid "Resource Time Off"
msgstr "Ferie risorsa"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Dettaglio ferie della risorsa"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar
msgid "Resource Working Time"
msgstr "Risorsa orario lavorativo"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__calendar_id
msgid "Resource's Calendar"
msgstr "Calendario risorse"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_resource_tree
#: model:ir.actions.act_window,name:resource.resource_resource_action_from_calendar
#: model:ir.model,name:resource.model_resource_resource
#: model:ir.model.fields,field_description:resource.field_res_users__resource_ids
#: model:ir.ui.menu,name:resource.menu_resource_resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_tree
msgid "Resources"
msgstr "Risorse"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_resources_leaves
msgid "Resources Time Off"
msgstr "Ferie delle risorse"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_resource_tree
#: model_terms:ir.actions.act_window,help:resource.resource_resource_action_from_calendar
msgid ""
"Resources allow you to create and manage resources that should be involved "
"in a specific project phase. You can also set their efficiency level and "
"workload based on their weekly working hours."
msgstr ""
"Risorse permettono di creare e gestire risorse che possono essere coinvolte "
"in una specifica fase di un progetto. È inoltre possibile indicare un "
"livello di efficienza e carico di lavoro basato sulle loro ore di lavoro "
"settimanali."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__5
msgid "Saturday"
msgstr "Sabato"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Search Resource"
msgstr "Ricerca risorsa"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Search Working Period Time Off"
msgstr "Ricerca ferie periodo lavorativo"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
msgid "Search Working Time"
msgstr "Ricerca orario lavorativo"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__1
msgid "Second"
msgstr "Secondo/a"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "Second week"
msgstr "Seconda settimana"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__display_type__line_section
msgid "Section"
msgstr "Sezione"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__share
msgid "Share User"
msgstr "Condividi utente"

#. module: resource
#. odoo-python
#: code:addons/resource/models/res_company.py:0
msgid "Standard 40 hours/week"
msgstr "40 ore/settimana regolari"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_from
msgid "Start Date"
msgstr "Data inizio"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__hour_from
msgid ""
"Start and End time of working.\n"
"A specific value of 24:00 is interpreted as 23:59:59.999999."
msgstr ""
"Orario di inizio e fine del lavoro.\n"
"Un valore specifico di 24:00 viene interpretato come 23:59:59.999999."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_from
msgid "Starting Date"
msgstr "Data inizio"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Starting Date of Time Off"
msgstr "Data di inizio delle ferie"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__6
msgid "Sunday"
msgstr "Domenica"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch"
msgstr "Cambia"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 1 week calendar"
msgstr "Passa a calendario 1 settimana"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 2 weeks calendar"
msgstr "Passa a calendario 2 settimane"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__display_type
msgid "Technical field for UX purpose."
msgstr "Campo tecnico per l'esperienza utente (UX)."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid ""
"The current week (from %(first_day)s to %(last_day)s) corresponds to week "
"number %(number)s."
msgstr ""
"La settimana corrente (dal %(first_day)s al %(last_day)s) corrisponde alla "
"settimana numero %(number)s."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_leaves.py:0
msgid "The start date of the time off must be earlier than the end date."
msgstr ""
"La data di inizio delle ferie deve essere precedente alla data di fine."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__tz
#: model:ir.model.fields,help:resource.field_resource_mixin__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Questo campo è utilizzato per definire in quale fuso orario lavora la "
"risorsa."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""
"Campo usato per calcolare la durata prevista di un ordine nel centro di "
"lavoro. Se, ad esempio, un ordine di lavoro impiega un'ora con fattore di "
"efficienza pari a 100%, la durata prevista corrisponde a un'ora. Ma se il "
"fattore di efficienza è 200%, è prevista una durata di 30 minuti."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__3
msgid "Thursday"
msgstr "Giovedì"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Thursday Afternoon"
msgstr "Giovedì pomeriggio"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Thursday Lunch"
msgstr "Pranzo del giovedì"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Thursday Morning"
msgstr "Giovedì mattina"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__leave_ids
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__leave
msgid "Time Off"
msgstr "Ferie"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Time Off Detail"
msgstr "Dettaglio ferie"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__time_type
msgid "Time Type"
msgstr "Tipo di tempo"

#. module: resource
#: model:ir.model.constraint,message:resource.constraint_resource_resource_check_time_efficiency
msgid "Time efficiency must be strictly positive"
msgstr "L'efficienza temporale deve essere strettamente positiva"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz
#: model:ir.model.fields,field_description:resource.field_resource_mixin__tz
#: model:ir.model.fields,field_description:resource.field_resource_resource__tz
msgid "Timezone"
msgstr "Fuso orario"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz_offset
msgid "Timezone offset"
msgstr "Offset fuso orario"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__1
msgid "Tuesday"
msgstr "Martedì"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Tuesday Afternoon"
msgstr "Martedì pomeriggio"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Tuesday Lunch"
msgstr "Pranzo del martedì"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Tuesday Morning"
msgstr "Martedì mattina"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__resource_type
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Type"
msgstr "Tipologia"

#. module: resource
#: model:ir.model,name:resource.model_res_users
#: model:ir.model.fields,field_description:resource.field_resource_resource__user_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "User"
msgstr "Utente"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__2
msgid "Wednesday"
msgstr "Mercoledì"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Wednesday Afternoon"
msgstr "Mercoledì pomeriggio"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Wednesday Lunch"
msgstr "Pranzo del mercoledì"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Wednesday Morning"
msgstr "Mercoledì mattina"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__week_type
msgid "Week Number"
msgstr "Numero Settimana"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__flexible_hours
msgid ""
"When enabled, it will allow employees to work flexibly, without relying on "
"the company's working schedule (working hours)."
msgstr ""
"Quando abilitato, permetterà ai dipendenti di lavorare in maniera flessibile"
" senza dover dipendere dall'orario di lavoro dell'azienda (orario "
"lavorativo)."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__time_type
msgid ""
"Whether this should be computed as a time off or as work time (eg: "
"formation)"
msgstr ""
"Indica se deve essere calcolato come ferie o come orario di lavoro (es. "
"formazione)"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Dettaglio lavoro"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_from
msgid "Work from"
msgstr "Inizio lavoro"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_to
msgid "Work to"
msgstr "Fine lavoro"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_ids
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Working Hours"
msgstr "Orario lavorativo"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Working Hours of %s"
msgstr "Orario lavorativo di %s"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_form
#: model:ir.ui.menu,name:resource.menu_resource_calendar
msgid "Working Schedules"
msgstr "Orari lavorativi"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__attendance_ids
#: model:ir.model.fields,field_description:resource.field_resource_resource__calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Working Time"
msgstr "Orario lavorativo"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "You can't delete section between weeks."
msgstr "Non puoi cancellare la sezione tra una settimana e l'altra."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "first"
msgstr "primo"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "other week"
msgstr "altra settimana"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "second"
msgstr "secondo"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "this week"
msgstr "questa settimana"
