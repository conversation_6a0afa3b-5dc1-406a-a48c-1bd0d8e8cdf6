# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_update_tax_tags
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__company_id
msgid "Company"
msgstr "Компания"

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__create_uid
msgid "Created by"
msgstr "Создано"

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__create_date
msgid "Created on"
msgstr "Создано"

#. module: account_update_tax_tags
#: model:ir.model.fields,help:account_update_tax_tags.field_account_update_tax_tags_wizard__date_from
msgid "Date from which journal items will be updated."
msgstr ""

#. module: account_update_tax_tags
#: model_terms:ir.ui.view,arch_db:account_update_tax_tags.view_account_update_tax_tags_wizard_form
msgid "Discard"
msgstr "Отменить"

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__display_lock_date_warning
msgid "Display Lock Date Warning"
msgstr ""

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__date_from
msgid "Starting from"
msgstr ""

#. module: account_update_tax_tags
#: model_terms:ir.ui.view,arch_db:account_update_tax_tags.view_account_update_tax_tags_wizard_form
msgid ""
"The date you chose is violating the tax lock date, do this at your own risk."
msgstr ""

#. module: account_update_tax_tags
#: model_terms:ir.ui.view,arch_db:account_update_tax_tags.view_account_update_tax_tags_wizard_form
msgid "Update"
msgstr "Обновить"

#. module: account_update_tax_tags
#: model:ir.model,name:account_update_tax_tags.model_account_update_tax_tags_wizard
msgid "Update Tax Tags Wizard"
msgstr ""

#. module: account_update_tax_tags
#: model:ir.actions.act_window,name:account_update_tax_tags.action_open_wizard
#: model_terms:ir.ui.view,arch_db:account_update_tax_tags.res_config_settings_view_form
msgid "Update tax tags on existing Journal Entries"
msgstr "Обновить налоговые теги в существующих записях журнала"

#. module: account_update_tax_tags
#. odoo-python
#: code:addons/account_update_tax_tags/wizard/account_update_tax_tags_wizard.py:0
msgid ""
"Update with children taxes that are child of multiple parents is not "
"supported."
msgstr ""

#. module: account_update_tax_tags
#: model_terms:ir.ui.view,arch_db:account_update_tax_tags.view_account_update_tax_tags_wizard_form
msgid ""
"Updating tax tags on existing Journal Entries is an <b>irreversible</b> action that will impact\n"
"                    your reports.<br/>\n"
"                    It is highly recommended to backup your database beforehand.<br/>\n"
"                    The update will change tax tags on your accounting history, starting from and including selected date,\n"
"                    so that it matches with the current configuration of your taxes.<br/>"
msgstr ""
"Обновление налоговых тегов в записях журнала — <b>необратимое</b> действие, \n"
"                  которое повлияет на ваши отчёты.<br/> \n"
"                  Рекомендуется заранее сделать резервную копию базы данных.<br/> \n"
"                  Изменения затронут налоговые теги в бухгалтерской истории, начиная с выбранной даты, \n"
"чтобы они соответствовали текущим настройкам налогов.<br/>"
