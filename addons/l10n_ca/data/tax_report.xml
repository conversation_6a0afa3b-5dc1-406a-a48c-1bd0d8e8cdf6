<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="l10n_ca_tr_gsthst" model="account.report">
        <field name="name">GST/HST report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.ca"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="l10n_ca_tr_gsthst_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_ca_tr_gsthst_90" model="account.report.line">
                <field name="name">90 - Taxable sales including zero-rated supplies (other than zero‑rated exports) made in Canada</field>
                <field name="code">Ca90</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_gsthst_90_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Ca90</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_gsthst_91" model="account.report.line">
                <field name="name">91 - Exempt supplies and zero-rated exports</field>
                <field name="code">Ca91</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_gsthst_91_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Ca91</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_gsthst_101" model="account.report.line">
                <field name="name">101 - Sales and other revenues</field>
                <field name="code">Ca101</field>
                <field name="hierarchy_level">1</field>
                <field name="aggregation_formula">Ca90.balance + Ca91.balance</field>
            </record>
            <record id="l10n_ca_tr_gsthst_103" model="account.report.line">
                <field name="name">103 - GST and HST amounts collected or that became collectible</field>
                <field name="code">Ca103</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_gsthst_103_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Ca103</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_gsthst_104" model="account.report.line">
                <field name="name">104 - Adjustments to be added to the net tax for the reporting period (for example, the GST/HST obtained from the recovery of a bad debt).</field>
                <field name="code">Ca104</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_gsthst_104_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Ca104</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_gsthst_105" model="account.report.line">
                <field name="name">105 - GST/HST and adjustments (103 + 104)</field>
                <field name="code">Ca105</field>
                <field name="aggregation_formula">Ca103.balance + Ca104.balance</field>
            </record>
            <record id="l10n_ca_tr_gsthst_106" model="account.report.line">
                <field name="name">106 - GST/HST you paid or that is payable by you on qualifying expenses (input tax credits – ITCs) for the current period and any eligible unclaimed ITCs from a previous period</field>
                <field name="code">Ca106</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_gsthst_106_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Ca106</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_gsthst_107" model="account.report.line">
                <field name="name">107 - Adjustments to be deducted when determining the net tax for the reporting period (for example, the GST/HST included in a bad debt)</field>
                <field name="code">Ca107</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_gsthst_107_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Ca107</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_gsthst_108" model="account.report.line">
                <field name="name">108 - ITCs and adjustments</field>
                <field name="code">Ca108</field>
                <field name="aggregation_formula">Ca106.balance + Ca107.balance</field>
            </record>
            <record id="l10n_ca_tr_gsthst_109" model="account.report.line">
                <field name="name">109 - Net tax</field>
                <field name="code">Ca109</field>
                <field name="aggregation_formula">Ca105.balance - Ca108.balance</field>
            </record>
            <record id="l10n_ca_tr_gsthst_110" model="account.report.line">
                <field name="name">110 - Instalment and other annual filer payments</field>
                <field name="code">Ca110</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_gsthst_110_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Ca110</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_gsthst_111" model="account.report.line">
                <field name="name">111 - GST/HST rebates claimable</field>
                <field name="code">Ca111</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_gsthst_111_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Ca111</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_gsthst_112" model="account.report.line">
                <field name="name">112 - Total other credits (if applicable)</field>
                <field name="code">Ca112</field>
                <field name="aggregation_formula">Ca110.balance + Ca111.balance</field>
            </record>
            <record id="l10n_ca_tr_gsthst_113A" model="account.report.line">
                <field name="name">113A - Balance after credits</field>
                <field name="code">Ca113A</field>
                <field name="aggregation_formula">Ca109.balance - Ca112.balance</field>
            </record>
            <record id="l10n_ca_tr_gsthst_205" model="account.report.line">
                <field name="name">205 - GST/HST due on purchases of real property or purchases of emission allowances</field>
                <field name="code">Ca205</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_gsthst_205_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Ca205</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_gsthst_405" model="account.report.line">
                <field name="name">405 - GST/HST to be self-assessed</field>
                <field name="code">Ca405</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_gsthst_405_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Ca405</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_gsthst_113B" model="account.report.line">
                <field name="name">113B - Total other debits (if applicable)</field>
                <field name="code">Ca113B</field>
                <field name="aggregation_formula">Ca205.balance + Ca405.balance</field>
            </record>
            <record id="l10n_ca_tr_gsthst_113C" model="account.report.line">
                <field name="name">113C - Balance after debits</field>
                <field name="code">Ca113C</field>
                <field name="aggregation_formula">Ca113A.balance - Ca113B.balance</field>
            </record>
            <record id="l10n_ca_tr_gsthst_114" model="account.report.line">
                <field name="name">114 - Refund Claimable</field>
                <field name="code">Ca114</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_gsthst_114_aggr" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">-Ca113C.balance</field>
                        <field name="subformula">if_above(CAD(0))</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_gsthst_115" model="account.report.line">
                <field name="name">115 - Amount to pay</field>
                <field name="code">Ca115</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_gsthst_115_aggr" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">Ca113C.balance</field>
                        <field name="subformula">if_above(CAD(0))</field>
                    </record>
                </field>
            </record>
        </field>
    </record>

    <record id="l10n_ca_tr_pst_bc" model="account.report">
        <field name="name">British-Columbia PST report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.ca"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="l10n_ca_tr_pst_bc_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_ca_tr_pst_bc_a" model="account.report.line">
                <field name="name">A - Sales and Leases (excluding GST/PST)</field>
                <field name="code">CaBcA</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_pst_bc_a_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaBcA</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_bc_B" model="account.report.line">
                <field name="name">B - PST Collectable on Sales and Leases</field>
                <field name="code">CaBcB</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_pst_bc_b_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaBcB</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_bc_c" model="account.report.line">
                <field name="name">C - Commission</field>
                <field name="code">CaBcC</field>
                <!--
                We need to handle very special computation for commissions
                if CaBcB.balance <= 22 then CaBcB.balance
                elif CaBcB.balance <= 333.33 then 22
                elif CaBcB.balance <= 3000 then CaBcB.balance * 6.6%
                else: => 198
                -->
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_pst_bc_c_under_22" model="account.report.expression">
                        <field name="label">under_22</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">CaBcB.balance - 22</field>
                        <field name="subformula">if_below(CAD(0))</field>
                    </record>
                    <record id="l10n_ca_tr_pst_bc_c_under_333" model="account.report.expression">
                        <field name="label">under_333</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">(CaBcB.balance - 333.33) * 0.066</field>
                        <field name="subformula">if_above(CAD(0))</field>
                    </record>
                    <record id="l10n_ca_tr_pst_bc_c_over_3000" model="account.report.expression">
                        <field name="label">over_3000</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">-(CaBcB.balance - 3000) * 0.066</field>
                        <field name="subformula">if_below(CAD(0))</field>
                    </record>
                    <record id="l10n_ca_tr_pst_bc_c_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">22 + CaBcC.under_22 + CaBcC.under_333 + CaBcC.over_3000</field>
                        <field name="subformula">if_above(CAD(0))</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_bc_d" model="account.report.line">
                <field name="name">D - Net PST Due on Sales and Leases</field>
                <field name="code">CaBcD</field>
                <field name="aggregation_formula">CaBcB.balance - CaBcC.balance</field>
            </record>
            <record id="l10n_ca_tr_pst_bc_e" model="account.report.line">
                <field name="name">E - Purchase and Lease Price of Taxable Goods, Software and Services from which no PST was paid</field>
                <field name="code">CaBcE</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_pst_bc_g_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaBcE</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_bc_f" model="account.report.line">
                <field name="name">F - PST Due on Purchases and Leases</field>
                <field name="code">CaBcF</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_pst_bc_f_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaBcF</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_bc_g" model="account.report.line">
                <field name="name">G - PST Payable Before Adjustments</field>
                <field name="code">CaBcG</field>
                <field name="aggregation_formula">CaBcD.balance + CaBcF.balance</field>
            </record>
            <record id="l10n_ca_tr_pst_bc_h" model="account.report.line">
                <field name="name">H - PST on Bad Debt Write-Off</field>
                <field name="code">CaBcH</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_pst_bc_h_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaBcH</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_bc_i" model="account.report.line">
                <field name="name">I - PST on Amounts Refunded or Credited to Customers</field>
                <field name="code">CaBcI</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_pst_bc_i_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaBcI</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_bc_j" model="account.report.line">
                <field name="name">J - Total Adjustments</field>
                <field name="code">CaBcJ</field>
                <field name="aggregation_formula">CaBcH.balance + CaBcI.balance</field>
            </record>
            <record id="l10n_ca_tr_pst_bc_k" model="account.report.line">
                <field name="name">K - Total Amount Due</field>
                <field name="code">CaBcK</field>
                <field name="aggregation_formula">CaBcG.balance - CaBcJ.balance</field>
            </record>
        </field>
    </record>

    <record id="l10n_ca_tr_pst_mb" model="account.report">
        <field name="name">Manitoba PST report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.ca"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="l10n_ca_tr_pst_mb_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_ca_tr_pst_mb_1" model="account.report.line">
                <field name="name">1 - Tax collectable on sales</field>
                <field name="code">CaMb1</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_pst_mb_1_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaMb1</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_mb_2" model="account.report.line">
                <field name="name">2 - Commission</field>
                <field name="code">CaMb2</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <!--
                    We need to handle very special computation for commissions
                    if CaMb1.balance <= 3 then CaMb1.balance
                    elif CaMb1.balance <= 20 then 3
                    elif CaMb1.balance <= 3000 then (max(200, CaMb1.balance) * 15 + min(0, CaMb1.balance-200)) / 100
                    else: => 0
                    -->
                    <record id="l10n_ca_tr_pst_mb_2_under_3" model="account.report.expression">
                        <field name="label">under_3</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">CaMb1.balance - 3</field>
                        <field name="subformula">if_below(CAD(0))</field>
                    </record>
                    <record id="l10n_ca_tr_pst_mb_2_under_200" model="account.report.expression">
                        <field name="label">under_200</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">(CaMb1.balance - 20) * 0.15</field>
                        <field name="subformula">if_above(CAD(0))</field>
                    </record>
                    <record id="l10n_ca_tr_pst_mb_2_under_3000" model="account.report.expression">
                        <field name="label">under_3000</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">-(CaMb1.balance - 200) * 0.14</field>
                        <field name="subformula">if_below(CAD(0))</field>
                    </record>
                    <record id="l10n_ca_tr_pst_mb_2_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">3 + CaMb2.under_3 + CaMb2.under_200 + CaMb2.under_3000</field>
                        <field name="subformula">if_between(CAD(0), CAD(58))</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_mb_3" model="account.report.line">
                <field name="name">3 - Tax Owing on Purchases</field>
                <field name="code">CaMb3</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_pst_mb_3_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaMb3</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_mb_35" model="account.report.line">
                <field name="name">Outstanding Balance Including Interest</field>
                <field name="code">CaMb35</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_pst_mb_35_balance" model="account.report.expression">
                   <field name="label">balance</field>
                    <field name="engine">external</field>
                    <field name="formula">sum</field>
                    <field name="subformula">editable;rounding=2</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_mb_4" model="account.report.line">
                <field name="name">4 - Total Amount Due</field>
                <field name="code">CaMb4</field>
                <field name="aggregation_formula">CaMb1.balance - CaMb2.balance + CaMb3.balance + CaMb35.balance</field>
            </record>
        </field>
    </record>

    <record id="l10n_ca_tr_qst" model="account.report">
        <field name="name">Quebec Tax report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.ca"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="l10n_ca_tr_pst_qst_gsthst" model="account.report.column">
                <field name="name">GST/HST</field>
                <field name="expression_label">gsthst</field>
            </record>
            <record id="l10n_ca_tr_pst_qst_qst" model="account.report.column">
                <field name="name">QST</field>
                <field name="expression_label">qst</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_ca_tr_qst_101" model="account.report.line">
                <field name="name">101 - Total supplies</field>
                <field name="code">CaQc101</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_101_tag" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc101</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_qst_103_203" model="account.report.line">
                <field name="name">103 - 203 - Tax amounts collectible</field>
                <field name="code">CaQc103203</field>
                <field name="hierarchy_level">5</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_103_tag" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc103</field>
                    </record>
                    <record id="l10n_ca_tr_qst_203_tag" model="account.report.expression">
                        <field name="label">qst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc203</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_qst_104_204" model="account.report.line">
                <field name="name">104 - 204 - Tax amounts adjustments</field>
                <field name="code">CaQc104204</field>
                <field name="hierarchy_level">5</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_104_tag" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc104</field>
                    </record>
                    <record id="l10n_ca_tr_qst_204_tag" model="account.report.expression">
                        <field name="label">qst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc204</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_qst_105_205" model="account.report.line">
                <field name="name">105 - 205 - Tax amounts collectible and adjustments</field>
                <field name="code">CaQc105205</field>
                <field name="hierarchy_level">4</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_105_aggr" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">CaQc103203.gsthst + CaQc104204.gsthst</field>
                    </record>
                    <record id="l10n_ca_tr_qst_205_aggr" model="account.report.expression">
                        <field name="label">qst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">CaQc103203.qst + CaQc104204.qst</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_qst_106_206" model="account.report.line">
                <field name="name">106 - 206 - ITCs/ITRs</field>
                <field name="code">CaQc106206</field>
                <field name="hierarchy_level">5</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_106_tag" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc106</field>
                    </record>
                    <record id="l10n_ca_tr_qst_206_tag" model="account.report.expression">
                        <field name="label">qst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc206</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_qst_107_207" model="account.report.line">
                <field name="name">107 - 207 - ITCs/ITRs amounts adjustments</field>
                <field name="code">CaQc107207</field>
                <field name="hierarchy_level">5</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_107_tag" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc107</field>
                    </record>
                    <record id="l10n_ca_tr_qst_207_tag" model="account.report.expression">
                        <field name="label">qst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc207</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_qst_108_208" model="account.report.line">
                <field name="name">108 - 208 - ITCs/ITRs and adjustments</field>
                <field name="code">CaQc108208</field>
                <field name="hierarchy_level">4</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_108_aggr" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">CaQc106206.gsthst + CaQc107207.gsthst</field>
                    </record>
                    <record id="l10n_ca_tr_qst_208_aggr" model="account.report.expression">
                        <field name="label">qst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">CaQc106206.qst + CaQc107207.qst</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_qst_109_209" model="account.report.line">
                <field name="name">109 - 209 - Net tax amounts</field>
                <field name="code">CaQc109209</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_109_aggr" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">CaQc105205.gsthst - CaQc108208.gsthst</field>

                    </record>
                    <record id="l10n_ca_tr_qst_209_aggr" model="account.report.expression">
                        <field name="label">qst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">CaQc105205.qst - CaQc108208.qst</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_qst_111_211" model="account.report.line">
                <field name="name">111 - 211 - Tax public service bodies' rebate</field>
                <field name="code">CaQc111211</field>
                <field name="hierarchy_level">3</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_111_tag" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc111</field>
                    </record>
                    <record id="l10n_ca_tr_qst_211_tag" model="account.report.expression">
                        <field name="label">qst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc211</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_qst_113_213" model="account.report.line">
                <field name="name">113 - 213 - Tax payable or refund claimed</field>
                <field name="code">CaQc113213</field>
                <field name="hierarchy_level">2</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_113_aggr" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">CaQc109209.gsthst - CaQc111211.gsthst</field>
                    </record>
                    <record id="l10n_ca_tr_qst_213_aggr" model="account.report.expression">
                        <field name="label">qst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">CaQc109209.qst - CaQc111211.qst</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_qst_114_214" model="account.report.line">
                <field name="name">114 - 214 - Tax payable on taxable immovables or taxable carbon emission allowances</field>
                <field name="code">CaQc114214</field>
                <field name="hierarchy_level">2</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_114_tag" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc114</field>
                    </record>
                    <record id="l10n_ca_tr_qst_214_tag" model="account.report.expression">
                        <field name="label">qst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc214</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_qst_115" model="account.report.line">
                <field name="name">115 - Tax payable on imported taxable supplies</field>
                <field name="code">CaQc115</field>
                <field name="hierarchy_level">2</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_115_tag" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaQc115</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_qst_116_216" model="account.report.line">
                <field name="name">116 - 216 - Tax payable or refund claimed</field>
                <field name="code">CaQc116216</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_qst_116_aggr" model="account.report.expression">
                        <field name="label">gsthst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">CaQc113213.gsthst + CaQc114214.gsthst + CaQc115.gsthst</field>
                    </record>
                    <record id="l10n_ca_tr_qst_216_aggr" model="account.report.expression">
                        <field name="label">qst</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">CaQc113213.qst + CaQc114214.qst</field>
                    </record>
                </field>
            </record>
        </field>
    </record>

    <record id="l10n_ca_tr_pst_sk" model="account.report">
        <field name="name">Saskatchewan PST report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.ca"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="l10n_ca_tr_pst_sk_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_ca_tr_pst_sk_total_sales" model="account.report.line">
                <field name="name">Total Sales Before Taxes</field>
                <field name="code">CaSkTotalSales</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="l10n_ca_tr_pst_sk_total_sales_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">CaSk8Base</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_sk_step_1" model="account.report.line">
                <field name="name">Step 1</field>
                <field name="code">CaSkStep1</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="l10n_ca_tr_pst_sk_1" model="account.report.line">
                        <field name="name">1 - Credits Carried Forward from Previous Period</field>
                        <field name="code">CaSk1</field>
                        <field name="hierarchy_level">3</field>
                        <field name="expression_ids">
                            <record id="l10n_ca_tr_pst_sk_1_balance" model="account.report.expression">
                           <field name="label">balance</field>
                            <field name="engine">external</field>
                            <field name="formula">sum</field>
                            <field name="subformula">editable;rounding=2;if_above(CAD(0))</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_ca_tr_pst_sk_2" model="account.report.line">
                        <field name="name">2 - Tax paid on Purchased for Resale</field>
                        <field name="code">CaSk2</field>
                        <field name="hierarchy_level">5</field>
                        <field name="expression_ids">
                            <record id="l10n_ca_tr_pst_sk_2_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">CaSk2</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_ca_tr_pst_sk_3" model="account.report.line">
                        <field name="name">3 - Tax Refunded to Customers</field>
                        <field name="code">CaSk3</field>
                        <field name="hierarchy_level">5</field>
                        <field name="expression_ids">
                            <record id="l10n_ca_tr_pst_sk_3_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">CaSk3</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_ca_tr_pst_sk_4" model="account.report.line">
                        <field name="name">4 - Tax Written Off on Bad Debts</field>
                        <field name="code">CaSk4</field>
                        <field name="hierarchy_level">5</field>
                        <field name="expression_ids">
                            <record id="l10n_ca_tr_pst_sk_4_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">CaSk4</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_ca_tr_pst_sk_5" model="account.report.line">
                        <field name="name">5 - Other</field>
                        <field name="code">CaSk5</field>
                        <field name="hierarchy_level">5</field>
                        <field name="expression_ids">
                            <record id="l10n_ca_tr_pst_sk_5_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">CaSk5</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_ca_tr_pst_sk_6" model="account.report.line">
                        <field name="name">6 - Tax Credits Recorded for this Period</field>
                        <field name="code">CaSk6</field>
                        <field name="hierarchy_level">3</field>
                        <field name="aggregation_formula">CaSk2.balance + CaSk3.balance + CaSk4.balance + CaSk5.balance</field>
                    </record>
                    <record id="l10n_ca_tr_pst_sk_7" model="account.report.line">
                        <field name="name">7 - Tax Credits</field>
                        <field name="code">CaSk7</field>
                        <field name="aggregation_formula">CaSk1.balance + CaSk6.balance</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_sk_step_2" model="account.report.line">
                <field name="name">Step 2</field>
                <field name="code">CaSkStep2</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="l10n_ca_tr_pst_sk_8" model="account.report.line">
                        <field name="name">8 - Tax Collected on Sales in this Period</field>
                        <field name="code">CaSk8</field>
                        <field name="hierarchy_level">3</field>
                        <field name="expression_ids">
                            <record id="l10n_ca_tr_pst_sk_8_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">CaSk8</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_ca_tr_pst_sk_9" model="account.report.line">
                        <field name="name">9 - Portion of Credits Applied</field>
                        <field name="code">CaSk9</field>
                        <field name="hierarchy_level">3</field>
                        <field name="expression_ids">
                            <record id="l10n_ca_tr_pst_sk_9_overflow" model="account.report.expression">
                                <!-- Avoid the CaSk9.balance to go above the value of CaSk8 and CaSk10.balance to go negative -->
                                <field name="label">overflow</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">CaSk8.balance - CaSk7.balance</field>
                                <field name="subformula">if_below(CAD(0))</field>
                            </record>
                            <record id="l10n_ca_tr_pst_sk_9_aggr" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">CaSk7.balance + CaSk9.overflow</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_ca_tr_pst_sk_10" model="account.report.line">
                        <field name="name">10 - Net Tax Collected</field>
                        <field name="code">CaSk10</field>
                        <field name="expression_ids">
                            <record id="l10n_ca_tr_pst_sk_10_aggr" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">CaSk8.balance - CaSk9.balance</field>
                                <field name="subformula">if_above(CAD(0))</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_ca_tr_pst_sk_11" model="account.report.line">
                        <field name="name">11 - Remaining Tax Credits</field>
                        <field name="code">CaSk11</field>
                        <field name="aggregation_formula">CaSk7.balance - CaSk9.balance</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ca_tr_pst_sk_step_3" model="account.report.line">
                <field name="name">Step 3</field>
                <field name="code">CaSkStep3</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="l10n_ca_tr_pst_sk_12" model="account.report.line">
                        <field name="name">12 - Consumption Tax</field>
                        <field name="code">CaSk12</field>
                        <field name="hierarchy_level">3</field>
                        <field name="expression_ids">
                            <record id="l10n_ca_tr_pst_sk_12_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">CaSk12</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_ca_tr_pst_sk_13" model="account.report.line">
                        <field name="name">13 - Portion of Credits Applied</field>
                        <field name="code">CaSk13</field>
                        <field name="hierarchy_level">3</field>
                        <field name="expression_ids">
                            <record id="l10n_ca_tr_pst_sk_13_overflow" model="account.report.expression">
                                <!-- Avoid the CaSk13.balance to go above the value of CaSk12 and CaSk14.balance to go negative -->
                                <field name="label">overflow</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">CaSk12.balance - CaSk11.balance</field>
                                <field name="subformula">if_below(CAD(0))</field>
                            </record>
                            <record id="l10n_ca_tr_pst_sk_13_aggr" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">CaSk11.balance + CaSk13.overflow</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_ca_tr_pst_sk_14" model="account.report.line">
                        <field name="name">14 - Net Consumption Tax</field>
                        <field name="code">CaSk14</field>
                        <field name="expression_ids">
                            <record id="l10n_ca_tr_pst_sk_14_aggr" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">CaSk12.balance - CaSk13.balance</field>
                                <field name="subformula">if_above(CAD(0))</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_ca_tr_pst_sk_15" model="account.report.line">
                        <field name="name">15 - Remaining Tax Credits</field>
                        <field name="code">CaSk15</field>
                        <field name="expression_ids">
                            <record id="l10n_ca_tr_pst_sk_15_aggr" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">CaSk11.balance - CaSk13.balance</field>
                                <field name="subformula">if_above(CAD(0))</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
