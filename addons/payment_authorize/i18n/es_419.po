# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_authorize
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid ""
"<select id=\"o_authorize_account_type\" required=\"\" class=\"form-select\">\n"
"                        <option value=\"checking\">Personal Checking</option>\n"
"                        <option value=\"savings\">Personal Savings</option>\n"
"                        <option value=\"businessChecking\">Business Checking</option>\n"
"                    </select>"
msgstr ""
"<select id=\"o_authorize_account_type\" required=\"\" class=\"form-select\">\n"
"                        <option value=\"checking\">Cheques personales</option>\n"
"                        <option value=\"savings\">Ahorros personales</option>\n"
"                        <option value=\"businessChecking\">Cheques de la empresa</option>\n"
"                    </select>"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "ABA Routing Number"
msgstr "Número de ruta ABA"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_client_key
msgid "API Client Key"
msgstr "Clave de cliente API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_login
msgid "API Login ID"
msgstr "ID de inicio de sesión de la API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_signature_key
msgid "API Signature Key"
msgstr "Clave de firma API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__authorize_transaction_key
msgid "API Transaction Key"
msgstr "Clave de la transacción API"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Account Number"
msgstr "Número de cuenta"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_provider__code__authorize
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.Net Profile ID"
msgstr "ID de perfil de Authorize.Net"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Account Type"
msgstr "Tipo de cuenta bancaria"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Name"
msgstr "Nombre del banco"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Code"
msgstr "Código de tarjeta"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Number"
msgstr "Número de tarjeta"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_provider__code
msgid "Code"
msgstr "Código"

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
msgid ""
"Could not fetch merchant details:\n"
"%s"
msgstr ""
"No pudimos obtener los detalles del comerciante:\n"
"%s"

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid ""
"Could not retrieve the transaction details. (error code: %(error_code)s; "
"error_details: %(error_message)s)"
msgstr ""
"No fue posible obtener los detalles de la transacción. (Código de error: "
"%(error_code)s; detalles del error: %(error_message)s) "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Expiration"
msgstr "Vencimiento"

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
msgid ""
"Failed to authenticate.\n"
"%s"
msgstr ""
"Error de autenticación.\n"
"%s"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_provider_form
msgid "Generate Client Key"
msgstr "Generar clave de cliente"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_provider_form
msgid "How to get paid with Authorize.Net"
msgstr "Cómo recibir pagos con Authorize.Net"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "MM"
msgstr "MM"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Name On Account"
msgstr "Nombre en la cuenta"

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "No se encontró ninguna transacción que coincida con la referencia %s."

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
msgid "Only one currency can be selected by Authorize.Net account."
msgstr "Solo puede seleccionar una moneda por cuenta de Authorize.Net. "

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_provider
msgid "Payment Provider"
msgstr "Proveedor de pago"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr "Token de pago"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de pago"

#. module: payment_authorize
#. odoo-javascript
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr "Error al procesar el pago"

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid "Received data with status code \"%(status)s\" and error code \"%(error)s\""
msgstr ""
"Se recibió información con el código de estado \"%(status)s\" y código de "
"error \"%(error)s\""

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/controllers/main.py:0
msgid "Received tampered payment request data."
msgstr "Datos recibidos de solicitud de pago manipulados."

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_provider_form
msgid "Set Account Currency"
msgstr "Configurar la moneda de la cuenta"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_provider__authorize_login
msgid "The ID solely used to identify the account with Authorize.Net"
msgstr "El ID que se usa solo para identificar la cuenta con Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_provider__authorize_client_key
msgid ""
"The public client key. To generate directly from Odoo or from Authorize.Net "
"backend."
msgstr ""
"La llave de cliente pública. Para generarla desde Odoo o el backend de "
"Authorize.Net."

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "El código técnico de este proveedor de pagos."

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid ""
"The transaction is not in a status to be refunded. (status: %(status)s, "
"details: %(message)s)"
msgstr ""
"La transacción no está en un estado en el que se pueda reembolsar. (Estado: "
"%(status)s, detalles: %(message)s)"

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr "La transacción no está vinculada a un token."

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"The unique reference for the partner/token combination in the Authorize.net "
"backend."
msgstr ""
"La referencia única para la combinación de contacto y token en el backend de"
" Authorize.Net."

#. module: payment_authorize
#. odoo-python
#: code:addons/payment_authorize/models/payment_provider.py:0
msgid "This action cannot be performed while the provider is disabled."
msgstr "Esta acción no se puede realizar si el proveedor está deshabilitado."

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "YY"
msgstr "YY"
