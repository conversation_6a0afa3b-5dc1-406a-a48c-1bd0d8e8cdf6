<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_gw" model="res.partner" forcecreate="1">
        <field name="name">GW Company</field>
        <field name="vat"></field>
        <field name="street"></field>
        <field name="city"></field>
        <field name="country_id" ref="base.gw"/>
        <field name="zip"></field>
        <field name="phone">+245 847 73 10</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.guinea-bissauexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_gw" model="res.company" forcecreate="1">
        <field name="name">GW Company</field>
        <field name="partner_id" ref="base.partner_demo_company_gw"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_gw')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_gw'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>gw</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_gw')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
