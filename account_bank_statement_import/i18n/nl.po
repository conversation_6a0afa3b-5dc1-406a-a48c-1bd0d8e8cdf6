# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import
# 
# Translators:
# Wil Odoo, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "%d transactions had already been imported and were ignored."
msgstr "%d transacties werden reeds geïmporteerd en werden genegeerd."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "1 transaction had already been imported and was ignored."
msgstr "1 transactie werd reeds geïmporteerd en werd genegeerd."

#. module: account_bank_statement_import
#: model:ir.model.constraint,message:account_bank_statement_import.constraint_account_bank_statement_line_unique_import_id
msgid "A bank account transactions can be imported only once!"
msgstr ""
"De transacties van een bankrekening kunnen slechts eenmaal geïmporteerd "
"worden!"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Rekeningafschriftregel"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_setup_bank_manual_config
msgid "Bank setup manual config"
msgstr "Bank installatie handmatige configuratie"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"Cannot find in which journal import this statement. Please manually select a"
" journal."
msgstr ""
"Kan niet bepalen in welk dagboek dit afschrift moet worden geimporteerd. "
"Selecteer handmatig een dagboek."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_bank_statement.py:0
msgid "Click \"New\" or upload a %s."
msgstr "Druk op 'Nieuw' of upload een%s."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"Could not make sense of the given file.\n"
"Did you install the module to support this type of file?"
msgstr ""
"Kon het opgegeven bestand niet begrijpen. \n"
"Heb je de module geïnstalleerd die dit bestandstype ondersteunt?"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "Go to Apps"
msgstr "Ga naar Apps"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "Import File"
msgstr "Bestand importeren"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_line__unique_import_id
msgid "Import ID"
msgstr "Import ID"

#. module: account_bank_statement_import
#. odoo-javascript
#: code:addons/account_bank_statement_import/static/src/account_bank_statement_import_model.js:0
msgid "Import Template for Bank Statements"
msgstr "Importeersjabloon voor rekeningafschriften"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_journal
msgid "Journal"
msgstr "Dagboek"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "Manual (or import %(import_formats)s)"
msgstr "Handmatig (of importeer %(import_formats)s)"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "No attachment was provided"
msgstr "Er was geen bijlage aangeleverd"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "No currency found matching '%s'."
msgstr "Geen overeenkomende valuta gevonden voor '%s'."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_bank_statement.py:0
msgid "No transactions matching your filters were found."
msgstr "Er zijn geen transacties gevonden die overeenkomen met je filters."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_bank_statement.py:0
msgid "Nothing to do here!"
msgstr "Niets te doen hier!"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"The account of this statement (%(account)s) is not the same as the journal "
"(%(journal)s)."
msgstr ""
"De rekening van dit afschrift (%(account)s) is niet hetzelfde als het "
"dagboek (%(journal)s)."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"The currency of the bank statement (%(code)s) is not the same as the "
"currency of the journal (%(journal)s)."
msgstr ""
"De valuta van de bankafschriften (%(code)s) is niet hetzelfde als dat van de"
" valuta op het dagboek (%(journal)s)."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "The following files could not be imported:\n"
msgstr "De volgende dossiers konden niet worden geïmporteerd:\n"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"This file doesn't contain any statement for account %s.\n"
"If it contains transactions for more than one account, it must be imported on each of them."
msgstr ""
"Dit bestand bevat geen afschriften voor rekening %s.\n"
"Als het transacties voor meer dan één rekening bevat, moet het voor elke rekening worden geïmporteerd."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"This file doesn't contain any transaction for account %s.\n"
"If it contains transactions for more than one account, it must be imported on each of them."
msgstr ""
"Dit bestand bevat geen transacties voor rekening %s.\n"
"Als het transacties voor meer dan één rekening bevat, moet het voor elke rekening worden geïmporteerd."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "View successfully imported statements"
msgstr "De succesvol geïmporteerd afschriften weergeven"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "You already have imported that file."
msgstr "Je hebt dit bestand al geïmporteerd."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "You have to set a Default Account for the journal: %s"
msgstr "Je moet een standaardrekening voor het dagboek instellen: %s"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "You uploaded an invalid or empty file."
msgstr "Je heb een ongeldig of leeg dossier geüpload."
