# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_starshipit
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:44+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid " after deleting the order on Starshipit"
msgstr " après avoir supprimé la commande sur Starshipit"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> Do not forget to select a service "
"for a valid configuration."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> N'oubliez pas de sélectionner un "
"service pour valider la configuration."

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> In <b>Test Environment</b>, your shippings are automatically <b>archived</b> after the label creation. <br/>\n"
"                        Please note that charges can still occur for label creations and some shipping carriers may automatically validate the shipment when printing labels."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> En <b>Environment de test</b>, vos envois sont automatiquement  <b>archivés</b> après la création de l'étiquette. <br/>\n"
"                        Veuillez noter que des frais peuvent encore survenir pour la création d'étiquettes et que certains transporteurs sont susceptibles de valider automatiquement l'envoi lors de l'impression d'étiquettes."

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid ""
"<i class=\"fa fa-info-circle\"/> Available shipping services depend on "
"enabled carriers in your Starshipit account."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Les modes d'expédition disponibles "
"dépendent des transporteurs activés dans votre compte Starshipit."

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid "Cancel"
msgstr "Annuler"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Transporteur"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Choose Starshipit Shipping Service"
msgstr "Choisir le service d'expédition Starshipip"

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_starshipit_shipping_wizard
msgid "Choose from the available starshipit shipping methods"
msgstr "Choisissez parmi les modes d'expédition Starshipit disponibles"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid "Confirm"
msgstr "Confirmer"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_starshipit_shipping_wizard__available_services
msgid ""
"Contains the list of available services for the starshipit account to select"
" from."
msgstr "Contient la liste des services disponibles pour le compte Starshipit."

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__create_date
msgid "Created on"
msgstr "Créé le"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Default Package Type"
msgstr "Type de colis par défaut"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_default_package_type_id
msgid "Default Package Type for Starshipit"
msgstr "Type de colis par défaut pour Starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__carrier_id
msgid "Delivery"
msgstr "Livraison"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid "Delivery Service"
msgstr "Service de livraison"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Error: %(file_name)s file could not be obtained for order %(order_name)s."
msgstr ""
"Erreur : le fichier %(file_name)s n'a pas pu être obtenu pour la commande "
"%(order_name)s."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Error: this delivery method is not available for this order."
msgstr ""
"Erreur : ce mode de livraison n'est pas disponible pour cette commande."

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__id
msgid "ID"
msgstr "ID"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Integration"
msgstr "Intégration"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Invalid Starshipit credentials."
msgstr "Identifiants Starshipit invalides."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Labels were generated for the order %s"
msgstr "Des étiquettes ont été générées pour la commande %s"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Options"
msgstr "Options"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Order %(order)s was already sent to the carrier during label creation.\n"
"Manifest number: %(manifest_number)s"
msgstr ""
"La commande %(order)s a été envoyée au transporteur lors de la création de l'étiquette.\n"
"Numéro du manifeste : %(manifest_number)s"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Order %(order)s was sent to the carrier during label creation.As you are in a test environment, please make sure to cancel the order with your carrier directly.\n"
"Manifest number: %(manifest_number)s"
msgstr ""
"La commande %(order)s a été envoyée au transporteur lors de la création de l'étiquette. Comme il s'agit d'un environnement de test, assurez-vous d'annuler la commande directement auprès de votre transporteur.\n"
"Numéro du manifeste : %(manifest_number)s"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Order %s was archived."
msgstr "La commande %s a été archivée."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Order %s was sent to the carrier."
msgstr "La commande %s a été envoyée au transporteur."

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_origin_address
msgid "Origin Address"
msgstr "Adresse d'origine"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_default_package_type_id
msgid ""
"Package dimensions are required to get more accurate rates. You can define "
"these in a package type that you set as default"
msgstr ""
"Les dimensions du colis sont nécessaires pour obtenir des tarifs plus "
"précis. Vous pouvez les définir dans le type de colis que vous définissez "
"par défaut"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Please delete the order on Starshipit then try again."
msgstr "Veuillez supprimer la commande sur Starshipit et réessayer."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Please fill in the field %(field)s on the %(partner)s partner."
msgstr "Veuillez compléter le champ %(field)s du partenaire %(partner)s."

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Fournisseur"

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_stock_return_picking
msgid "Return Picking"
msgstr "Transfert de retour"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Return labels were generated for the order %s"
msgstr "Des étiquettes de retour ont été générées pour la commande %s"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Return order %(order)s was already sent to the carrier during label creation.\n"
"Manifest number: %(manifest_number)s"
msgstr ""
"L'ordre de retour %(order)s a été envoyée au transporteur lors de la création de l'étiquette.\n"
"Numéro du manifeste : %(manifest_number)s"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Return order %(order)s was sent to the carrier during label creation.As you are in a test environment, please make sure to cancel the order with your carrier directly.\n"
"Manifest number: %(manifest_number)s"
msgstr ""
"L'ordre de retour %(order)s a été envoyé au transporteur lors de la création de l'étiquette. Comme il s'agit d'un environnement de test, assurez-vous de l'annuler directement auprès de votre transporteur.\n"
"Numéro du manifeste : %(manifest_number)s"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Return order %s was archived."
msgstr "L'ordre de retour %s a été archivé."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "Return order %s was sent to the carrier."
msgstr "L'ordre de retour %s a été envoyé au transporteur."

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Select a service linked to your starshipit account"
msgstr "Sélectionnez un service lié à votre compte Starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_starshipit_shipping_wizard__selected_service_code
msgid "Selected Service"
msgstr "Service sélectionné"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_service_code
msgid "Service Code"
msgstr "Code du service"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_service_name
msgid "Service Name"
msgstr "Nom du service"

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Modes d'expédition"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_shipping_method_starshipit
msgid "Shipping Product"
msgstr "Produit d'expédition"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Something went wrong, please try again later: %s"
msgstr "Une erreur s'est produite. Veuillez réessayer plus tard : %s"

#. module: delivery_starshipit
#: model:ir.model.fields.selection,name:delivery_starshipit.selection__delivery_carrier__delivery_type__starshipit
#: model:ir.model.fields.selection,name:delivery_starshipit.selection__stock_package_type__package_carrier_type__starshipit
msgid "Starshipit"
msgstr "Starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_api_key
msgid "Starshipit API Integration key"
msgstr "Clé d'intégration de l'API Starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_subscription_key
msgid "Starshipit API Subscription key"
msgstr "Clé d'abonnement de l'API Starshipit"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Starshipit API rate exceeded. Please try again later."
msgstr ""
"La limite de l'API Starshipit API a été dépassée. Veuillez réessayer plus "
"tard."

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_api_key
msgid "Starshipit Api Key"
msgstr "Clé de l'API Starshipit"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.view_delivery_carrier_form
msgid "Starshipit Configuration"
msgstr "Configuration de Starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_stock_picking__starshipit_parcel_reference
msgid "Starshipit Parcel Reference"
msgstr "Référence de colis Starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_stock_picking__starshipit_return_parcel_reference
msgid "Starshipit Return Parcel Reference"
msgstr "Référence du retour du colis Starshipit"

#. module: delivery_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.res_config_settings_view_form_sale_starshipit
#: model_terms:ir.ui.view,arch_db:delivery_starshipit.res_config_settings_view_form_stock_starshipit
msgid "Starshipit Shipping Methods"
msgstr "Modes d'expédition Starshipit"

#. module: delivery_starshipit
#: model:ir.model.fields,field_description:delivery_starshipit.field_delivery_carrier__starshipit_subscription_key
msgid "Starshipit Subscription Key"
msgstr "Clé d'abonnement Starshipit"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"Starshipit cannot generate returns for the carrier %s. Please handle this "
"return with the carrier directly."
msgstr ""
"Starshipit ne peut pas générer de retours pour le transporteur %s. Veuillez "
"traiter ce retour directement avec le transporteur."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid "Starshipit returned an error: %(message)s"
msgstr "Starshipit a renvoyé une erreur : %(message)s"

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_stock_package_type
msgid "Stock package type"
msgstr "Type de colis de stock"

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_carrier_code
msgid ""
"The carrier on starshipit used by this carrier. The service code belongs to "
"it."
msgstr ""
"Le transporteur Starshipit utilisé par ce transporteur. Le code du service "
"lui est propre."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid ""
"The picking %(picking_name)s sequence is too long for Starshipit. Please "
"update your pickings sequence in order to use at most 50 characters."
msgstr ""
"La séquence de transfert %(picking_name)s est trop longue pour Starshipit. "
"Veuillez mettre à jour votre séquence de transfert afin d'utiliser au "
"maximum 50 caractères."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "The return label creation failed."
msgstr "La création de l'étiquette de retour a échoué."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/starshipit_service.py:0
msgid ""
"The service code %(service_code)s is too long for Starshipit. Please update "
"the code inside starshipit to be at most 100 characters, then update your "
"shipping carrier %(shipping_carrier)s to the new code."
msgstr ""
"Le code du service %(service_code)s est trop long pour Starshipit. Veuillez "
"modifier votre code dans Starshipit afin d'utiliser au maximum 100 "
"caractères, puis mettez à jour votre transporteur %(shipping_carrier)s avec "
"le nouveau code."

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_service_code
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_service_name
msgid ""
"The service that will be used for this carrier. This is set when you select "
"a carrier from the wizard."
msgstr ""
"Le service qui sera utilisé pour ce transporteur. Celui-ci est défini "
"lorsque vous sélectionnez un transporteur dans l'assistant."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"The shipping label creation failed with the following error:\n"
"%(error)s"
msgstr ""
"La création de l'étiquette d'expédition a échoué en raison de l'erreur suivante :\n"
"%(error)s"

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid ""
"There are no shipping services available, please verify the shipping address"
" or activate suitable carriers in your starshipit account."
msgstr ""
"Aucun produit d'expédition n'est disponible. Veuillez vérifier l'adresse "
"d'expédition ou activez des transporteurs appropriés dans votre compte "
"Starshipit."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "There was an issue when creating the order, please try again"
msgstr ""
"Un problème est survenu lors de la création de la commande, veuillez "
"réessayer."

#. module: delivery_starshipit
#. odoo-python
#: code:addons/delivery_starshipit/models/delivery_carrier.py:0
msgid "This action requires a Starshipit carrier."
msgstr "Cette action nécessite un transporteur Starshipit."

#. module: delivery_starshipit
#: model:ir.model.fields,help:delivery_starshipit.field_delivery_carrier__starshipit_origin_address
msgid ""
"This address will be used when fetching the available services from "
"starshipit."
msgstr ""
"Cette adresse sera utilisée lors de la recherche de services Starshipit "
"disponibles."

#. module: delivery_starshipit
#: model:ir.model,name:delivery_starshipit.model_stock_picking
msgid "Transfer"
msgstr "Transfert"
