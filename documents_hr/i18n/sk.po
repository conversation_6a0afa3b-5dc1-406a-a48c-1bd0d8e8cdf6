# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 15:42+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.documents_hr_portal_view
msgid "<i class=\"fa fa-download fa-fw\"/> Download file"
msgstr ""

#. module: documents_hr
#: model:mail.template,body_html:documents_hr.mail_template_document_folder_link
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your personal documents access link is available for you.<br/><br/>\n"
"            You'll find all your personal HR documents in this folder.<br/><br/>\n"
"            Please keep this link secure.<br/><br/>\n"
"            <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                <a t-attf-href=\"{{ object._get_documents_link_url() }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                    Your Documents\n"
"                </a>\n"
"            </div>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_tag_absences
msgid "Absences"
msgstr "Neprítomnosť"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Centralize your employees' documents (contracts, payslips, etc.)"
msgstr ""
"Centralizujte dokumenty svojich zamestnancov (zmluvy, výplatné pásky, atď.)"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_tag_certification
msgid "Certifications"
msgstr "Certifikácie"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_company
msgid "Companies"
msgstr "Spoločnosti"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Sprievodca odchodom"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_ids
msgid "Document"
msgstr "Dokument"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_hr_employee__document_count
msgid "Document Count"
msgstr "Počet dokumentov"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_documents_redirect
msgid "Document Redirect"
msgstr ""

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_count
#: model_terms:ir.ui.view,arch_db:documents_hr.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:documents_hr.res_users_view_form
msgid "Documents"
msgstr "Dokumenty"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_settings
msgid "Documents Hr Settings"
msgstr "Nastavenia HR dokumentu"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.documents_hr_portal_view
msgid "Download file"
msgstr ""

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_hr_employee
#: model:ir.model.fields,field_description:documents_hr.field_documents_redirect__employee_id
msgid "Employee"
msgstr "Zamestnanec"

#. module: documents_hr
#. odoo-python
#: code:addons/documents_hr/models/hr_employee.py:0
msgid ""
"Employee's related user and private email must be set to use \"Send Access Link\" function:\n"
"%s"
msgstr ""

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_tag_employees
msgid "Employees Documents"
msgstr "Dokumenty zamestnancov"

#. module: documents_hr
#: model:mail.template,name:documents_hr.mail_template_document_folder_link
msgid "HR: Document Access Link"
msgstr ""

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_settings
msgid "Human Resources"
msgstr "Ľudské zdroje"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.documents_hr_portal_view
msgid "My documents"
msgstr ""

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_hr_departure_wizard__private_email
msgid "Private Email"
msgstr "Súkromný e-mail"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_hr_departure_wizard__send_hr_documents_access_link
msgid "Send Access Link"
msgstr ""

#. module: documents_hr
#: model:ir.actions.server,name:documents_hr.ir_actions_server_send_access_link
msgid "Send HR Documents Access Link"
msgstr ""

#. module: documents_hr
#: model:ir.model.fields,help:documents_hr.field_hr_departure_wizard__send_hr_documents_access_link
msgid ""
"Send a share link to the private email of the employee with all the HR files"
" he owns in Documents app"
msgstr ""

#. module: documents_hr
#: model:mail.template,description:documents_hr.mail_template_document_folder_link
msgid "Sent manually when recording an employee departure"
msgstr ""

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.documents_hr_portal_view
msgid "Size:"
msgstr "Veľkosť:"

#. module: documents_hr
#. odoo-python
#: code:addons/documents_hr/models/hr_employee.py:0
msgid "The access link has been sent to the employee."
msgstr ""

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_users
msgid "User"
msgstr "Užívateľ"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Workspace"
msgstr "Pracovný priestor"

#. module: documents_hr
#. odoo-python
#: code:addons/documents_hr/models/hr_employee.py:0
msgid "You can not send the documents link to the employee."
msgstr ""

#. module: documents_hr
#. odoo-python
#: code:addons/documents_hr/models/hr_employee.py:0
msgid ""
"You must set a work contact address on the Employee in order to use "
"Document's features."
msgstr ""

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_folder
msgid "hr Workspace"
msgstr "hr Pracovný priestor"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_folder
msgid "hr default workspace"
msgstr "hr pracovný priestor"

#. module: documents_hr
#: model:mail.template,subject:documents_hr.mail_template_document_folder_link
msgid "{{ object.name }}, your personal documents access link is available"
msgstr ""

#. module: documents_hr
#. odoo-python
#: code:addons/documents_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You must set a Human Resources Documents Workspace in your Settings to use "
"this feature."
msgstr ""
