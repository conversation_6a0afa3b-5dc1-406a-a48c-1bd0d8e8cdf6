# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_holidays
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# Rune Restad, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Rune Restad, 2024\n"
"Language-Team: <PERSON> Bokmål (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_payroll_holidays
#: model_terms:ir.actions.act_window,help:hr_payroll_holidays.hr_leave_work_entry_action
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr ""
"En flott måte å holde oversikt over ansattes ferie-, sykedager og "
"godkjenningsstatus på."

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_mail_activity
msgid "Activity"
msgstr "Aktivitet"

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_res_company
msgid "Companies"
msgstr "Firmaer"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__done
msgid "Computed in current payslip"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: hr_payroll_holidays
#: model:ir.actions.server,name:hr_payroll_holidays.ir_actions_server_report_to_next_month
msgid "Defer to Next Month"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Deferred Time Off"
msgstr "Utsatt fravær"

#. module: hr_payroll_holidays
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_res_company__deferred_time_off_manager
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_res_config_settings__deferred_time_off_manager
msgid "Deferred Time Off Manager"
msgstr "Utsatt fravær administrator"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.view_hr_leave_allocation_inherit_filter
msgid "Employee Code"
msgstr "Ansattkode"

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_hr_contract
msgid "Employee Contract"
msgstr "Ansattkontrakt"

#. module: hr_payroll_holidays
#: model:mail.activity.type,name:hr_payroll_holidays.mail_activity_data_hr_leave_to_defer
msgid "Leave to Defer"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.actions.server,name:hr_payroll_holidays.ir_actions_server_mark_as_reported
msgid "Mark as deferred"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.actions.act_window,help:hr_payroll_holidays.hr_leave_work_entry_action
msgid "Meet the time off dashboard."
msgstr "Dette er fraværs-dashbordet"

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid ""
"Not enough attendance work entries to report the time off %s. Please make "
"the operation manually"
msgstr ""

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid "Only an employee time off to defer can be reported to next month"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_hr_payslip
msgid "Pay Slip"
msgstr "Lønnsslipp"

#. module: hr_payroll_holidays
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_hr_leave__payslip_state
msgid "Payslip State"
msgstr "Lønnsslipp status"

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid "Please create manually the work entry for %s"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Postpone time off after payslip validation"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_form_inherit
msgid "Report to Next Month"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Responsible"
msgstr "Ansvarlig"

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid ""
"The next month work entries are not generated yet or are validated already "
"for time off %s"
msgstr ""

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid ""
"The pay of the month is already validated with this day included. If you "
"need to adapt, please refer to HR."
msgstr ""

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid ""
"The time off %s can not be reported because it is defined over more than 2 "
"months"
msgstr ""

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid "There is no work entries linked to this time off to report"
msgstr ""

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_payslip.py:0
msgid ""
"There is some remaining time off to defer for these employees: \n"
"\n"
" %s"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.actions.act_window,name:hr_payroll_holidays.hr_leave_work_entry_action
#: model:ir.model,name:hr_payroll_holidays.model_hr_leave
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Time Off"
msgstr "Fravær"

#. module: hr_payroll_holidays
#: model:hr.payroll.dashboard.warning,name:hr_payroll_holidays.hr_payroll_dashboard_warning_leaves_no_allocation
msgid "Time Off Not Related To An Allocation"
msgstr ""

#. module: hr_payroll_holidays
#: model:hr.payroll.dashboard.warning,name:hr_payroll_holidays.hr_payroll_dashboard_warning_leaves_to_defer
msgid "Time Off To Defer"
msgstr ""

#. module: hr_payroll_holidays
#: model:hr.payroll.dashboard.warning,name:hr_payroll_holidays.hr_payroll_dashboard_warning_leaves_no_document
msgid "Time Off Without Joined Document"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.actions.act_window,name:hr_payroll_holidays.hr_leave_action_open_to_defer
msgid "Time Off to Defer"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.ui.menu,name:hr_payroll_holidays.menu_work_entry_leave_to_approve
msgid "Time Off to Report"
msgstr "Fravær til rapportering"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_search
msgid "To Defer"
msgstr "Å utsette"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__normal
msgid "To compute in next payslip"
msgstr "Å beregne i neste lønnsslipp"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__blocked
msgid "To defer to next payslip"
msgstr ""

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid "Validated Time Off to Defer"
msgstr ""

#. module: hr_payroll_holidays
#. odoo-javascript
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
msgid "You have some"
msgstr ""

#. module: hr_payroll_holidays
#. odoo-javascript
#: code:addons/hr_payroll_holidays/static/src/views/hooks.js:0
msgid "You have some <button>time off</button> to defer to the next month."
msgstr ""

#. module: hr_payroll_holidays
#. odoo-javascript
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
msgid "time off"
msgstr "fravær"

#. module: hr_payroll_holidays
#. odoo-javascript
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
msgid "to defer to the next month."
msgstr ""
