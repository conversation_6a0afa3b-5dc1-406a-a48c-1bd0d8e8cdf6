<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_l10n_ch_hr_payslip_monthly_wizard_form" model="ir.ui.view">
        <field name="name">l10n_ch_hr_payslip_monthly_wizard.form</field>
        <field name="model">l10n.ch.hr.payslip.montlhy.wizard</field>
        <field name="arch" type="xml">
            <form string="Generate Monthly Pay">
                <sheet>
                <group>
                    <field name="name" placeholder="e.g. September 2025 - Monthly Batch"/>
                    <field name="year" string="Year" class="o_hr_narrow_field" options="{'type': 'number'}"/>
                    <field name="month"/>
                    <field name="employee_ids" widget="many2many_tags" placeholder="All Employees" options="{'no_open': True, 'no_create': True}"/>
                    <field name="workplace_id" widget="many2many_tags" placeholder="All Workplaces" options="{'no_open': True, 'no_create': True}"/>
                    <field name="department_id" widget="many2many_tags" placeholder="All Departments" options="{'no_open': True, 'no_create': True}"/>
                    <field name="contract_type" widget="many2many_tags" placeholder="All Contract Types" options="{'no_open': True, 'no_create': True}"/>
                    <field name="company_id"/>
                    <field name="pay_13th"/>
                </group>
                </sheet>
                <footer>
                    <button name="action_create" string="Create" type="object" class="oe_highlight"/>
                    <button string="Cancel" special="cancel" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_l10n_ch_hr_payslip_monthly_wizard" model="ir.actions.act_window">
        <field name="name">Generate Monthly Payslips</field>
        <field name="res_model">l10n.ch.hr.payslip.montlhy.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_l10n_ch_hr_payslip_monthly_wizard_form"/>
        <field name="target">new</field>
    </record>
</odoo>
