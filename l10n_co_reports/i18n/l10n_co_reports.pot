# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_co_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 08:21+0000\n"
"PO-Revision-Date: 2025-03-26 08:21+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_co_reports
#. odoo-python
#: code:addons/l10n_co_reports/models/general_ledger.py:0
msgid "(None)"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid ", practicó en la ciudad de"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_activo
msgid "1 ASSETS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_activo_11
msgid "11 CASH AND CASH EQUIVALENTS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_activo_12
msgid "12 INVESTMENTS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_activo_13
msgid "13 ACCOUNTS RECEIVABLE"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_activo_14
msgid "14 INVENTORIES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_activo_15
msgid "15 PROPERTY, PLANT AND EQUIPMENT"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_activo_16
msgid "16 INTANGIBLE ASSETS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_activo_17
msgid "17 DEFERRED ASSETS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_activo_18
msgid "18 OTHER ASSETS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_activo_19
msgid "19 REVALUATIONS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_pasivo
msgid "2 LIABILITIES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_pasivo_21
msgid "21 FINANCIAL OBLIGATIONS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_pasivo_22
msgid "22 TRADE PAYABLES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_pasivo_23
msgid "23 ACCOUNTS PAYABLE"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_pasivo_24
msgid "24 TAXES, LEVIES AND FEES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_pasivo_25
msgid "25 LABOR OBLIGATIONS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_pasivo_26
msgid "26 ESTIMATED LIABILITIES AND PROVISIONS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_pasivo_27
msgid "27 DEFERRED LIABILITIES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_pasivo_28
msgid "28 OTHER LIABILITIES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_pasivo_29
msgid "29 BONDS AND COMMERCIAL PAPER"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_patrimonio
msgid "3 EQUITY"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_patrimonio_31
msgid "31 SHARE CAPITAL"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_patrimonio_32
msgid "32 CAPITAL SURPLUS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_patrimonio_33
msgid "33 RESERVES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_patrimonio_34
msgid "34 EQUITY REVALUATION"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_patrimonio_35
msgid ""
"35 DIVIDENDS OR PARTICIPATIONS DECREED IN SHARES, QUOTAS OR PARTIES OF "
"SOCIAL INTEREST"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_patrimonio_36
msgid "36 NET INCOME FOR THE YEAR"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_patrimonio_37
msgid "37 RETAINED EARNINGS FROM PRIOR YEARS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_patrimonio_38
msgid "38 REVALUATION SURPLUS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_ingresos
msgid "4 INCOME"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_ingresos_41
msgid "41 OPERATING REVENUE"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_ingresos_42
msgid "42 NON-OPERATING REVENUE"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_ingresos_47
msgid "47 INFLATION ADJUSTMENTS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_gastos
msgid "5 EXPENSES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_gastos_51
msgid "51 ADMINISTRATIVE EXPENSES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_gastos_52
msgid "52 SELLING EXPENSES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_gastos_53
msgid "53 NON-OPERATING EXPENSES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_gastos_54
msgid "54 INCOME TAX AND SUPPLEMENTARY TAXES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_gastos_59
msgid "59 GAINS AND LOSSES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_c_ventas
msgid "6 COST OF SALES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_c_ventas_61
msgid "61 COST OF GOODS SOLD AND SERVICES RENDERED"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_c_ventas_62
msgid "62 PURCHASES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_c_prod
msgid "7 PRODUCTION OR OPERATION COSTS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_c_prod_71
msgid "71 RAW MATERIALS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_c_prod_72
msgid "72 DIRECT LABOR"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_c_prod_73
msgid "73 INDIRECT COSTS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_c_prod_74
msgid "74 SERVICE CONTRACTS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_deud
msgid "8 MEMORANDUM ACCOUNTS – DEBIT BALANCES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_deud_81
msgid "81 CONTINGENT RIGHTS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_deud_82
msgid "82 TAX DEBTORS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_deud_83
msgid "83 CONTROL DEBTORS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_deud_84
msgid "84 OFFSET CONTINGENT RIGHTS (CR)"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_deud_85
msgid "85 OFFSET TAX DEBTORS (CR)"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_deud_86
msgid "86 OFFSET CONTROL DEBTORS (CR)"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_acre
msgid "9 MEMORANDUM ACCOUNTS – CREDIT BALANCES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_acre_91
msgid "91 CONTINGENT LIABILITIES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_acre_92
msgid "92 TAX CREDITORS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_acre_93
msgid "93 CONTROL CREDITORS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_acre_94
msgid "94 OFFSET CONTINGENT LIABILITIES (DB)"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_acre_95
msgid "95 OFFSET TAX CREDITORS (DB)"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_cu_acre_96
msgid "96 OFFSET CONTROL CREDITORS (DB)"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_libro_diario
msgid "<span>DAILY JOURNAL REPORT</span>"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "<strong>%</strong>"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "<strong>Bimestre</strong>"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "<strong>Concepto de retención</strong>"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "<strong>MONTO DEL PAGO SUJETO A:</strong>"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "<strong>MONTO TOTAL OPERACIONAL:</strong>"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "<strong>Monto Total Operación</strong>"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "<strong>Monto del Pago Sujeto Retención</strong>"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "<strong>Monto del pago sujeto a retención</strong>"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "<strong>RETENIDO Y CONSIGNADO:</strong>"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "<strong>Retenido Consignado</strong>"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "<strong>Retenido y consignado</strong>"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "<strong>TOTAL DE IMPUESTOS DESCONTABLES</strong>"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_libro_diario_account_name
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_libro_diario
msgid "ACCOUNT"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_assets
msgid "ASSETS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_payable
msgid "Accounts Payable"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_expenses_admin
msgid "Administrative Operations"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model.fields,field_description:l10n_co_reports.field_l10n_co_reports_retention_report_wizard__article
msgid "Artículo"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_inv_blc_assets_expenses
msgid "Assets + Expenses"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_bs_report_column
#: model:account.report.column,name:l10n_co_reports.l10n_co_pl_account_report_column
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_libro_inv_blc_column
msgid "Balance"
msgstr ""

#. module: l10n_co_reports
#: model:account.report,name:l10n_co_reports.l10n_co_bs_report
msgid "Balance Sheet"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_cash
msgid "Bank and Cash"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_ica_bimestre
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_iva_bimestre
msgid "Bimestre"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_bonds
msgid "Bonds and Commercial Paper"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "CERTIFICADO DE RETENCIÓN DE IVA"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "CERTIFICADO DE RETENCIÓN EN ICA"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "CERTIFICADO DE RETENCIÓN POR TERCEROS"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_libro_diario_line_credit
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_libro_diario
msgid "CREDIT"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_capital_surplus
msgid "Capital Surplus"
msgstr ""

#. module: l10n_co_reports
#: model:ir.actions.client,name:l10n_co_reports.action_account_report_fuente
#: model:ir.ui.menu,name:l10n_co_reports.menu_action_account_report_fuente
msgid "Certificado de Retención en Fuente"
msgstr ""

#. module: l10n_co_reports
#: model:ir.actions.client,name:l10n_co_reports.action_account_report_ica
#: model:ir.ui.menu,name:l10n_co_reports.menu_action_account_report_ica
msgid "Certificado de Retención en ICA"
msgstr ""

#. module: l10n_co_reports
#: model:ir.actions.client,name:l10n_co_reports.action_account_report_iva
#: model:ir.ui.menu,name:l10n_co_reports.menu_action_account_report_iva
msgid "Certificado de Retención en IVA"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.retention_report_wizard_form
msgid "Certificados de Retención"
msgstr ""

#. module: l10n_co_reports
#: model:ir.actions.report,name:l10n_co_reports.action_report_certification
msgid "Certification Report"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "Ciudad Tercero:"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model,name:l10n_co_reports.model_report_l10n_co_reports_report_certification
msgid "Colombian Certification Report"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model,name:l10n_co_reports.model_report_l10n_co_reports_report_libro_diario
msgid "Colombian Libro Diario Report"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model,name:l10n_co_reports.model_l10n_co_reports_retention_report_wizard
msgid "Colombian Retention Report Wizard"
msgstr ""

#. module: l10n_co_reports
#: model:ir.ui.menu,name:l10n_co_reports.account_reports_co_statements_menu
msgid "Colombian Statements"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model,name:l10n_co_reports.model_l10n_co_report_handler
msgid "Columbian Report Custom Handler"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_fuente_communication
msgid "Concepto de retención"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_cost_production
msgid "Cost of Production"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_sales_cost
msgid "Cost of Sales"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_cost_sales_services
msgid "Cost of sales and provision of services"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model.fields,field_description:l10n_co_reports.field_l10n_co_reports_retention_report_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model.fields,field_description:l10n_co_reports.field_l10n_co_reports_retention_report_wizard__create_date
msgid "Created on"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_assets_current
msgid "Current Assets"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_liabilities_current
msgid "Current Liabilities"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_year_earnings
msgid "Current Year Unallocated Earnings"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_libro_diario_line_date
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_libro_diario
msgid "DATE"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_libro_diario_line_debit
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_libro_diario
msgid "DEBIT"
msgstr ""

#. module: l10n_co_reports
#: model:account.report,name:l10n_co_reports.l10n_co_reports_libro_diario
#: model:ir.actions.client,name:l10n_co_reports.action_account_report_libro_diario
#: model:ir.actions.report,name:l10n_co_reports.action_report_libro_diario
#: model:ir.ui.menu,name:l10n_co_reports.menu_action_account_report_libro_diario
msgid "Daily Journal Report"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_ac_debt
msgid "Debtors"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_ac_deferred
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_anc_deferred
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_deferred_liabilities
msgid "Deferred"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_depreciation
msgid "Depreciation and Amortization"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid ""
"Dicha Retención fue consignada oportunamente a nombre de la Dirección de "
"Impuestos y Aduanas Nacionales en la Ciudad de"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "Dirección:"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_cost_labor
msgid "Direct Labor"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model.fields,field_description:l10n_co_reports.field_l10n_co_reports_retention_report_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_dividends
msgid ""
"Dividends or Participations Declared in Shares, Quotas or Parts of Social "
"Interest"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "Durante el año gravable de"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_equity
msgid "EQUITY"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid ""
"El presente certificado se expide en concordancia con las disposiciones "
"legales contenidas en el artículo 381 del Estatuto Tributario."
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_provisions
msgid "Estimated Liabilities and Provisions"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_expenses
msgid "Expenses"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_libro_diario
msgid "FROM"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model.fields,field_description:l10n_co_reports.field_l10n_co_reports_retention_report_wizard__declaration_date
msgid "Fecha de Declaración"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "Fecha de Declaración:"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model.fields,field_description:l10n_co_reports.field_l10n_co_reports_retention_report_wizard__expedition_date
msgid "Fecha de Expedición"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "Fecha de Expedición:"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.retention_report_wizard_form
msgid "Fechas para Certificado"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_fin_obligations
msgid "Financial Obligations"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model,name:l10n_co_reports.model_l10n_co_fuente_report_handler
msgid "Fuente Report Custom Handler"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_gains_losses
msgid "Gains and Losses"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model,name:l10n_co_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model,name:l10n_co_reports.model_l10n_co_ica_report_handler
msgid "ICA Report Custom Handler"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model.fields,field_description:l10n_co_reports.field_l10n_co_reports_retention_report_wizard__id
msgid "ID"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model,name:l10n_co_reports.model_l10n_co_iva_report_handler
msgid "IVA Report Custom Handler"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.retention_report_wizard_form
msgid "Imprimir"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_income
msgid "Income"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_income_tax
msgid "Income Tax"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_cost_indirect
msgid "Indirect Costs"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_intangibles
msgid "Intangibles"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_ac_inventory
msgid "Inventory"
msgstr ""

#. module: l10n_co_reports
#: model:account.report,name:l10n_co_reports.l10n_co_reports_libro_inv_blc
msgid "Inventory and Balance Report"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_investments
msgid "Investments"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_libro_diario_line_label
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_libro_diario
msgid "LABEL"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_liabilities
msgid "LIABILITIES"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_liabilities_equity
msgid "LIABILITIES AND EQUITY"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_labor
msgid "Labor Obligations"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model.fields,field_description:l10n_co_reports.field_l10n_co_reports_retention_report_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model.fields,field_description:l10n_co_reports.field_l10n_co_reports_retention_report_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_inv_blc_equity_liability
msgid "Liabilities + Equity + Income"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model,name:l10n_co_reports.model_l10n_co_libro_diario_report_handler
msgid "Libro Diario Report Custom Handler"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_libro_diario_move_name
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_libro_diario
msgid "MOVE"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_iva_tax_base_amount
msgid "Monto Total Operación"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_fuente_tax_base_amount
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_iva_balance_15_over_19
msgid "Monto del Pago Sujeto Retención"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_ica_tax_base_amount
msgid "Monto del pago sujeto a retención"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_libro_diario
msgid "NIT:"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "NO REQUIRE FIRMA AUTOGRAFA."
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_net_income
msgid "Net Income"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_income_before_tax
msgid "Net Income before Tax"
msgstr ""

#. module: l10n_co_reports
#. odoo-python
#: code:addons/l10n_co_reports/report/libro_diario_report.py:0
msgid "No lines were provided to print."
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "Nombre/Razón Social:"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_assets_non_current
msgid "Non-current Assets"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_liabilities_non_current
msgid "Non-current Liabilities"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_expenses_non_operational
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_income_non_operational
msgid "Non-operational"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_income_operational
msgid "Operational"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_assets_other
msgid "Other Assets"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_liabilities_current_other
msgid "Other Current Liabilities"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_liabilities_non_current_other
msgid "Other Non-current Liabilities"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_libro_diario_partner_name
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_libro_diario
msgid "PARTNER"
msgstr ""

#. module: l10n_co_reports
#. odoo-python
#: code:addons/l10n_co_reports/models/trial_balance.py:0
msgid "Partner Name"
msgstr ""

#. module: l10n_co_reports
#. odoo-python
#: code:addons/l10n_co_reports/models/trial_balance.py:0
msgid "Partner VAT"
msgstr ""

#. module: l10n_co_reports
#. odoo-python
#: code:addons/l10n_co_reports/models/trial_balance.py:0
msgid "Partners information"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_previous_year_results
msgid "Previous Year Earnings"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr ""

#. module: l10n_co_reports
#: model:account.report,name:l10n_co_reports.l10n_co_pl_account_report_pymes
#: model:ir.actions.client,name:l10n_co_reports.action_l10n_co_pl_report
msgid "Profit and Loss"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_property
msgid "Property, Plant and Equipment"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_purchase_cost
msgid "Purchases"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_raw_material
msgid "Raw Material"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_reserves
msgid "Reserves"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_year_results
msgid "Results for the Year"
msgstr ""

#. module: l10n_co_reports
#: model:account.report,name:l10n_co_reports.l10n_co_reports_ica
msgid "Retención en ICA"
msgstr ""

#. module: l10n_co_reports
#: model:account.report,name:l10n_co_reports.l10n_co_reports_iva
msgid "Retención en IVA"
msgstr ""

#. module: l10n_co_reports
#: model:account.report,name:l10n_co_reports.l10n_co_reports_fuente
msgid "Retención por Terceros"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_fuente_balance
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_iva_balance
msgid "Retenido Consignado"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.column,name:l10n_co_reports.l10n_co_reports_ica_balance
msgid "Retenido y consignado"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_revaluation
msgid "Revaluation of Equity"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_expenses_sales
msgid "Sales Operations"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_pl_account_report_cost_contracts
msgid "Service Contracts"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid ""
"Señor contribuyente: Favor tener en cuenta que la base certificada por "
"nosotros corresponde a la sometida a retención durante el periodo gravable "
"según las normas vigentes sobre la materia y podría ser diferente a lo "
"facturado por usted durante el mismo periodo. al declarar tenerlo presente."
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_social_capital
msgid "Social Capital"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_suppliers
msgid "Suppliers"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_valuations_surplus
msgid "Surplus from Valuations"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_libro_diario
msgid "TO"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_taxes
msgid "Taxes, Levies and Fees"
msgstr ""

#. module: l10n_co_reports
#. odoo-python
#: code:addons/l10n_co_reports/models/general_ledger.py:0
msgid "Total"
msgstr ""

#. module: l10n_co_reports
#: model:ir.model,name:l10n_co_reports.model_account_trial_balance_report_handler
msgid "Trial Balance Custom Handler"
msgstr ""

#. module: l10n_co_reports
#: model:account.report.line,name:l10n_co_reports.l10n_co_bs_report_valuations
msgid "Valuations"
msgstr ""

#. module: l10n_co_reports
#. odoo-python
#: code:addons/l10n_co_reports/models/trial_balance.py:0
msgid "XLSX (By Partner)"
msgstr ""

#. module: l10n_co_reports
#. odoo-python
#: code:addons/l10n_co_reports/report/certification_report.py:0
msgid "You have to expand at least one partner."
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "con NIT:"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "retención de ICA a"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "retención de IVA a"
msgstr ""

#. module: l10n_co_reports
#: model_terms:ir.ui.view,arch_db:l10n_co_reports.report_certification
msgid "retención en la fuente a"
msgstr ""
