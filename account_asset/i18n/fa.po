# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_asset
# 
# Translators:
# z<PERSON><PERSON> moradi, 2025
# <PERSON>, 2025
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# Naser mars, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-20 18:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Naser mars, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciation_entries_count
msgid "# Depreciation Entries"
msgstr "# ثبت سند های استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_count
msgid "# Gross Increases"
msgstr "# افزایش های ناخالص"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_entries_count
msgid "# Posted Depreciation Entries"
msgstr "# ثبت سند های استهلاک ارسال شده"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "%(asset)s: Disposal"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "%(asset)s: Sale"
msgstr "%(asset)s: فروش"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "%(months)s m"
msgstr "%(months)s ماه"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "%(move_line)s (%(current)s of %(total)s)"
msgstr "%(move_line)s (%(current)s از %(total)s)"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "%(years)s y"
msgstr "%(years)s سال"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "%s (copy)"
msgstr "%s (کپی)"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"%s Future entries will be recomputed to depreciate the asset following the "
"changes."
msgstr ""
"%sاسناد آینده برای تعیین استهلاک دارایی پس از تغییرات دوباره محاسبه می‌شوند."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "%s: Depreciation"
msgstr "%s: استهلاک"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "(No %s)"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "(incl."
msgstr "(incl."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"A depreciation entry will be posted on and including the date %(date)s. "
"<br/> %(extra_text)s Future entries will be recomputed to depreciate the "
"asset following the changes."
msgstr ""
"یک سند استعهلاک در تاریخ مشخص شده ثبت خواهد شد %(date)s. <br/> "
"%(extra_text)s اسناد آینده برای تعیین استهلاک دارایی پس از تغییرات دوباره "
"محاسبه خواهند شد."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"A depreciation entry will be posted on and including the date %(date)s.<br/>"
" A disposal entry will be posted on the %(account_type)s account "
"<b>%(account)s</b>."
msgstr ""
"یک سند واگذاربی در تاریخ مشخص شده ثبت خواهد شد%(date)s. <br/> یک سند واگذاری"
" در %(account_type)s حساب ثبت خواهد شد <b></b>.%(account)s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"A depreciation entry will be posted on and including the date %(date)s.<br/>"
" A second entry will neutralize the original income and post the  outcome of"
" this sale on account <b>%(account)s</b>."
msgstr ""
"یگ سند استهلاک در تاریخ مشخص شده ثبت خواهد شد%(date)s. <br/> سند دوم درآمد "
"اولیه را خنثی کرده و نتیجه‌ی این فروش را در حساب ثبت "
"می‌کند<b></b>%(account)s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "A depreciation entry will be posted on and including the date %s."
msgstr "یک سند استهلاک در تاریخ مشخص شده ثبت خواهد شد  %s."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "A document linked to %(move_line_name)s has been deleted: %(link)s"
msgstr "سندی که با %(move_line_name)s در ارتباط بوده حذف شده است: %(link)s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "A document linked to this move has been deleted: %s"
msgstr "یک سند مرتبط با این سند حسابداری حذف شده است: %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "A gross increase has been created: %(link)s"
msgstr "افزایش ناخالص ایجاد شده است: %(link)s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"A non deductible tax value of %(tax_value)s was added to %(name)s's initial "
"value of %(purchase_value)s"
msgstr ""
"یک مقدار مالیات قابل کسر %(tax_value)s به مقدار اولیه %(name)s از "
"%(purchase_value)s اضافه شد."

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_account
msgid "Account"
msgstr "حساب"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_type
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"نوع حسابی که به منظور اطلاعات استفاده می‌شود، برای ایجاد گزارش‌های قانونی "
"خاص کشور، و مجموعه‌‌ای از قوانین برای بستن یک سال مالی و ایجاد سند افتتاحیه."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr "حساب استفاده شده در ورودی‌های استهلاک، برای کاهش ارزش دارایی."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"حساب استفاده شده در ثبت سند های دوره ای، برای ثبت بخشی از دارایی به صورت "
"هزینه."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr "حساب مورد استفاده برای ثبت خرید دارایی با قیمت اولیه آن."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__gain_account_id
msgid "Account used to write the journal item in case of gain"
msgstr "حساب مورد استفاده برای آیتم روزنامه در حالت سود کردن"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__gain_account_id
msgid ""
"Account used to write the journal item in case of gain while selling an "
"asset"
msgstr ""
"حساب مورد استفاده برای نوشتن آیتم روزنامه در حالت سود هنگام فروش یک دارایی"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__loss_account_id
msgid "Account used to write the journal item in case of loss"
msgstr "حساب مورد استفاده برای نوشتن آیتم روزنامه در حالت زیان"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__loss_account_id
msgid ""
"Account used to write the journal item in case of loss while selling an "
"asset"
msgstr ""
"حساب مورد استفاده برای نوشتن آیتم روزنامه در حالت زیان هنگام فروش یک دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Accounting"
msgstr "حسابداری"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_report
msgid "Accounting Report"
msgstr "گزارش حسابداری"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_acquisition_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset__acquisition_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Acquisition Date"
msgstr "تاریخ خرید"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__modify_action
msgid "Action"
msgstr "عمل"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction
msgid "Action Needed"
msgstr "اقدام مورد نیاز است"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__active
msgid "Active"
msgstr "فعال"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_ids
msgid "Activities"
msgstr "فعالیت ها"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "دکوراسیون استثنایی فعالیت"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_state
msgid "Activity State"
msgstr "وضعیت فعالیت"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_icon
msgid "Activity Type Icon"
msgstr "آیکون نوع فعالیت"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Add an internal note"
msgstr "افزودن یادداشت داخلی"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
msgid "All the lines should be from the same account"
msgstr "تمام سطر ها باید از یک حساب یکسان باشند"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "All the lines should be from the same company"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
msgid "All the lines should be posted"
msgstr "همه سطر ها باید ارسال شده باشند"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__already_depreciated_amount_import
msgid "Already Depreciated Amount Import"
msgstr "مبلغ استهلاک از قبل خروجی گرفته شده است"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__parent_id
msgid "An asset has a parent when it is the result of gaining value"
msgstr "یک دارایی دارای والد است وقتی نتیجه افزایش ارزش باشد"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "An asset has been created for this move:"
msgstr "یک دارایی برای این انتقال ایجاد شده است:"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__asset_model_ids
msgid ""
"An asset wil be created for each asset model when this account is used on a "
"vendor bill or a refund"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "An asset will be created for the value increase of the asset. <br/>"
msgstr "یک دارایی برای افزایش ارزش دارایی ثبت خواهد شد. <br/>"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_distribution
msgid "Analytic Distribution"
msgstr "توزیع تحلیلی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_precision
msgid "Analytic Precision"
msgstr "دقت تحلیلی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Archived"
msgstr "بایگانی شده"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset"
msgstr "دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "حساب دارایی"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset Cancelled"
msgstr "دارایی لغو شد"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_counterpart_id
msgid "Asset Counterpart Account"
msgstr "حساب دارایی همتا"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_group
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_group_id
#: model_terms:ir.ui.view,arch_db:account_asset.asset_group_form_view
#: model_terms:ir.ui.view,arch_db:account_asset.asset_group_list_view
msgid "Asset Group"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id_display_name
msgid "Asset Id Display Name"
msgstr "نام نمایشی شناسه دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_lifetime_days
msgid "Asset Lifetime Days"
msgstr "عمر سرمایه"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_model_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Asset Model"
msgstr "مدل دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Model name"
msgstr "نام مدل دارایی"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_model_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_tree
msgid "Asset Models"
msgstr "مدل های دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_move_type
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_move_type
msgid "Asset Move Type"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__name
msgid "Asset Name"
msgstr "نام دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_paused_days
msgid "Asset Paused Days"
msgstr "تعداد روزهای توقف دارایی "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_value_change
msgid "Asset Value Change"
msgstr "تغییر ارزش دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Values"
msgstr "ارزش های دارایی"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset created"
msgstr "دارایی ایجاد شده"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Asset created from invoice: %s"
msgstr "دارایی ایجاد شده از فاکتور: %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset disposed. %s"
msgstr "دارایی واگذار شده. %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset paused. %s"
msgstr "سرمایه‌ی متوقف شده. %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset sold. %s"
msgstr "سرمایه‌ی فروخته شده. %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Asset unpaused. %s"
msgstr "مسئول گزارشات گمرکی دارایی. %s"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_group_form_view
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_form_asset_inherit
msgid "Asset(s)"
msgstr "دارایی(ها)"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr "تشخیص دارایی/درآمد"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_asset_form
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_tree
msgid "Assets"
msgstr "دارایی ها"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_report_handler
msgid "Assets Report Custom Handler"
msgstr "مسئول گزارشات گمرکی دارایی‌ها"

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "دارایی‌ها و درآمدها"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "دارایی‌ها در حالت بسته شده"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "دارایی در پیشنویس و ایالات باز"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"Atleast one asset (%s) couldn't be set as running because it lacks any "
"required information"
msgstr ""
"حداقل یک دارایی (%s) را نمی‌توان به عنوان دارایی جاری مشخص کرد زیرا فاقد "
"هرگونه اطلاعات موردنیاز است"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_attachment_count
msgid "Attachment Count"
msgstr "تعداد پیوست ها"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Asset"
msgstr "اتوماسیون کردن دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automation"
msgstr "اتوماسیون"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__prorata_computation_type__daily_computation
msgid "Based on days per period"
msgstr "براساس روزهای هر دوره"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Bills"
msgstr "صورتحساب"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__book_value
msgid "Book Value"
msgstr "ارزش دفتری"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__can_create_asset
msgid "Can Create Asset"
msgstr "می تواند دارایی ایجاد کند"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Cancel"
msgstr "لغو"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Cancel Asset"
msgstr "لغو دارایی"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__cancelled
msgid "Cancelled"
msgstr "لغو شد"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Characteristics"
msgstr "خصوصیات"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__children_ids
msgid "Children"
msgstr "فرزندان"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Straight Line: Calculated on basis of: Gross Value / Duration\n"
"  * Declining: Calculated on basis of: Residual Value * Declining Factor\n"
"  * Declining then Straight Line: Like Declining but with a minimum depreciation value equal to the straight line value."
msgstr ""
"انتخاب روش مورد استفاده برای محاسبه‌ی مبلغ سطرهای استهلاک.\n"
"سطر مستقیم: محاسبه شده براساس: ارزش ناخالص/ مدت\n"
"سطر نزولی: محاسبه شده بر اساس: ارزش باقی‌مانده ضریب تنزل\n"
"سطر نزولی سپس مستقیم: مانند سطر نزولی اما با حداقل مقدار استهلاک برابر با ارزش سطر مستقیم."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__close
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "بسته شد"

#. module: account_asset
#: model:ir.model,name:account_asset.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__company_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__company_id
msgid "Company"
msgstr "شرکت"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_computation_type
msgid "Computation"
msgstr "محاسبه"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_asset_compute_depreciations
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Compute Depreciation"
msgstr "محاسبه استهلاک"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_asset_run
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Confirm"
msgstr "تایید کردن"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__prorata_computation_type__constant_periods
msgid "Constant Periods"
msgstr "دوره‌های ثابت"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__count_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move__count_asset
msgid "Count Asset"
msgstr "مقدار دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__count_linked_asset
msgid "Count Linked Asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__count_linked_assets
msgid "Count Linked Assets"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__country_code
msgid "Country Code"
msgstr "کد کشور"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_aml_to_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__create_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree
msgid "Create Asset"
msgstr "ایجاد دارایی"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__validate
msgid "Create and validate"
msgstr "ایجاد و تایید"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__draft
msgid "Create in draft"
msgstr "ایجاد در پیشنویس"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_form
msgid "Create new asset"
msgstr "ایجاد دارایی جدید"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_model_form
msgid "Create new asset model"
msgstr "ایجاد مدل دارایی جدید"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciated_value
msgid "Cumulative Depreciation"
msgstr "استهلاک انباشته"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__currency_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__currency_id
msgid "Currency"
msgstr "ارز"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "جاری"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Current Values"
msgstr "ارزش های فعلی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__invoice_ids
msgid "Customer Invoice"
msgstr "فاکتور مشتری"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Date"
msgstr "تاریخ"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciation_beginning_date
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciation_beginning_date
msgid "Date of the beginning of the depreciation"
msgstr "تاریخ شروع استهلاک"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Dec. then Straight"
msgstr "دسامبر و سپس مستقیم"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive
msgid "Declining"
msgstr "کاهشی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_progress_factor
msgid "Declining Factor"
msgstr "ضریب کاهش"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive_then_linear
msgid "Declining then Straight Line"
msgstr "کاهشی سپس خط مستقیم"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__value_residual
msgid "Depreciable Amount"
msgstr "مقدار قابل استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__value_residual
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_remaining_value
msgid "Depreciable Value"
msgstr "ارزش قابل استهلاک"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciated Amount"
msgstr "مقدار استهلاک شده"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__depreciation_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__depreciation_value
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__depreciation
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation"
msgstr "استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Depreciation Account"
msgstr "حساب استهلاک"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Board"
msgstr "بورد استهلاک"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Date"
msgstr "تاریخ استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_move_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Lines"
msgstr "سطرهای استهلاک"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Method"
msgstr "روش استهلاک"

#. module: account_asset
#: model:account.report,name:account_asset.assets_report
#: model:ir.actions.client,name:account_asset.action_account_report_assets
#: model:ir.ui.menu,name:account_asset.menu_action_account_report_assets
msgid "Depreciation Schedule"
msgstr "برنامه ریزی استهلاک"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Depreciation board modified %s"
msgstr "استهلاک اصلاح شده%s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Depreciation entry %(name)s posted (%(value)s)"
msgstr "ثبت سند استهلاک %(name)s ارسال شده (%(value)s)"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Depreciation entry %(name)s reversed (%(value)s)"
msgstr "ثبت سند استهلاک %(name)s رزرو شده (%(value)s)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_account_asset_id
msgid "Display Account Asset"
msgstr "نمایش حساب دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__disposal
msgid "Disposal"
msgstr "اسقاط"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__disposal_date
msgid "Disposal Date"
msgstr "تاریخ اسقاط"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Disposal Move"
msgstr "انتقال اسقاط"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Disposal Moves"
msgstr "انتقالهای اسقاط"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Dispose"
msgstr "اسقاط"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "حساب تحلیلی توزیعی"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__draft
msgid "Draft"
msgstr "پیش‌نویس"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__draft_asset_exists
#: model:ir.model.fields,field_description:account_asset.field_account_move__draft_asset_exists
msgid "Draft Asset Exists"
msgstr "پیشنویس دارایی موجود است"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_number
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Duration"
msgstr "مدت زمان"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_duration_rate
msgid "Duration / Rate"
msgstr "مدت زمان / نرخ"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_expense_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_expense_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Expense Account"
msgstr "حساب هزینه"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_first_depreciation
msgid "First Depreciation"
msgstr "اولین استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Fixed Asset Account"
msgstr "حساب دارایی ثابت"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_follower_ids
msgid "Followers"
msgstr "دنبال کنندگان"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "پیروان (شرکاء)"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "آیکون فونت عالی به عبارتی fa-tasks"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__form_view_ref
msgid "Form View Ref"
msgstr "شماره مرجع نمای فرم"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Future Activities"
msgstr "فعالیت های آینده"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__gain_or_loss__gain
msgid "Gain"
msgstr "سود"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__gain_account_id
msgid "Gain Account"
msgstr "حساب سود"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_or_loss
msgid "Gain Or Loss"
msgstr "سود یا زیان"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_value
msgid "Gain Value"
msgstr "ارزش سود"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Gross Increase"
msgstr "افزایش ناخالص"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_id
msgid "Gross Increase Account"
msgstr "حساب افزایش ناخالص"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_value
msgid "Gross Increase Value"
msgstr "ارزش افزایش ناخالص"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group By Account"
msgstr "دسته بندی بر اساس حساب"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group By Asset Group"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Group By..."
msgstr "گروه بتدی بر اساس ..."

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group by Account"
msgstr ""

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group by Asset Group"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__has_message
msgid "Has Message"
msgstr "آیا دارای پیام است"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__id
msgid "ID"
msgstr "شناسه"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_icon
msgid "Icon"
msgstr "شمایل"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "آیکون برای نشان دادن یک فعالیت استثنا."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"اگر این گزینه را انتخاب کنید، پیام‌های جدید به توجه شما نیاز خواهند داشت."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "اگر علامت زده شود، برخی از پیام ها دارای خطای تحویل هستند."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__already_depreciated_amount_import
msgid ""
"In case of an import from another software, you might need to use this field"
" to have the right depreciation table report. This is the value that was "
"already depreciated with entries not computed from this model"
msgstr ""
"در صورت ورود از نرم افزار دیگری، ممکن است لازم باشد از این قسمت برای داشتن "
"گزارش جدول استهلاک مناسب استفاده کنید. این مقداری است که قبلاً با ورودی های "
"محاسبه نشده از این مدل مستهلک شده است"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__informational_text
msgid "Informational Text"
msgstr "توضیحات"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__invoice_line_ids
msgid "Invoice Line"
msgstr "سطر فاکتور"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_is_follower
msgid "Is Follower"
msgstr "آیا دنبال می کند"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value_pct
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr ""
"مقداری است که برنامه‌ریزی می‌کنید داشته باشید که نمی‌توانید مستهلک کنید."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__journal_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Journal"
msgstr "روزنامه"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Journal Entries"
msgstr "داده های روزنامه"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Journal Entry"
msgstr "سند دفترروزنامه‌"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move_line
msgid "Journal Item"
msgstr "آیتم دفترروزنامه"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_move_line_ids
msgid "Journal Items"
msgstr "آیتم های روزنامه"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid ""
"Journal Items of %(account)s should have a label in order to generate an "
"asset"
msgstr ""
"آیتم‌های دفتر روزنامه‌ی %(account)s باید دارای برچسب باشند تا یک دارایی "
"ایجاد شود"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Late Activities"
msgstr "فعالیتهای اخیر"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Linear"
msgstr "خطی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__linked_assets_ids
msgid "Linked Assets"
msgstr ""

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__gain_or_loss__loss
msgid "Loss"
msgstr "زیان"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__loss_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__loss_account_id
msgid "Loss Account"
msgstr "حساب زیان"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Manage Items"
msgstr "مدیریت آیتم‌ها"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error
msgid "Message Delivery error"
msgstr "خطای تحویل پیام"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_ids
msgid "Messages"
msgstr "پیام ها"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_first_method
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method
msgid "Method"
msgstr "روش"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__model_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Model"
msgstr "مدل"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_properties_definition
msgid "Model Properties"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "تغییر"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "تغییر دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Modify Depreciation"
msgstr "تغییر استهلاک"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__1
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__1
msgid "Months"
msgstr "ماه"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__multiple_assets_per_line
msgid "Multiple Assets per Line"
msgstr "دارایی های متعدد در هر سظز"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__multiple_assets_per_line
msgid ""
"Multiple asset items will be generated depending on the bill line quantity "
"instead of 1 global asset."
msgstr ""
"بسته به مقدار سطر صورتحساب به جای 1 دارایی جهانی، چندین مورد دارایی ایجاد "
"خواهد شد."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "موعد نهای فعالیت من"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__name
msgid "Name"
msgstr "نام"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__negative_revaluation
msgid "Negative revaluation"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__net_gain_on_sale
msgid "Net gain on sale"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__net_gain_on_sale
msgid "Net value of gain or loss on sale of an asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__value_residual
msgid "New residual amount for the asset"
msgstr "مقدار باقیمانده جدید برای دارایی"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__salvage_value
msgid "New salvage amount for the asset"
msgstr "مقدار نجات یافته جدید برای دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "رویداد تقویم فعالیت بعدی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "موعد فعالیت بعدی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_summary
msgid "Next Activity Summary"
msgstr "خلاصه فعالیت بعدی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_id
msgid "Next Activity Type"
msgstr "نوع فعالیت بعدی"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__no
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__gain_or_loss__no
msgid "No"
msgstr "خیر"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "No Grouping"
msgstr ""

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__prorata_computation_type__none
msgid "No Prorata"
msgstr "بدون سرشکن"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__non_deductible_tax_value
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__non_deductible_tax_value
msgid "Non Deductible Tax Value"
msgstr "مالیات قابل کسر"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__salvage_value
msgid "Not Depreciable Amount"
msgstr "مقدار قابل استهلاک نیست"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value
msgid "Not Depreciable Value"
msgstr "ارزش قابل استهلاک نیست"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value_pct
msgid "Not Depreciable Value Percent"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__name
msgid "Note"
msgstr "یادداشت"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "تعداد اقدامات"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_tree
msgid "Number of Depreciations"
msgstr "تعداد استهلاکها"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_period
msgid "Number of Months in a Period"
msgstr "تعداد ماه‌ها در دوره"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__gross_increase_count
msgid "Number of assets made to increase the value of the asset"
msgstr "تعداد دارایی های ساخته شده برای افزایش ارزش دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_number_days
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_number_days
msgid "Number of days"
msgstr "تعداد روزها"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__total_depreciation_entries_count
msgid "Number of depreciation entries (posted or not)"
msgstr "تعداد ثبت سند های استهلاک (ارسال شده یا نشده)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error_counter
msgid "Number of errors"
msgstr "تعداد خطاها"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "تعداد پیام هایی که نیاز به اقدام دارند"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "تعداد پیامهای با خطای تحویل"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__paused
msgid "On Hold"
msgstr "در انتظار"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Open Asset"
msgstr "باز کردن دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_value
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Original Value"
msgstr "مقدار اولیه"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__parent_id
msgid "Parent"
msgstr "والد"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Parent Asset"
msgstr "دارایی والد"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Pause"
msgstr "مکث"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__paused_prorata_date
msgid "Paused Prorata Date"
msgstr "تاریخ توقف سرشکن"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_tree
msgid "Period length"
msgstr "طول دوره"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__positive_revaluation
msgid "Positive revaluation"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Posted Entries"
msgstr "ثبت سند های ارسال شده"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_properties
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Properties"
msgstr "مشخصات"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_date
msgid "Prorata Date"
msgstr "تاریخ سرشکن"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__purchase
msgid "Purchase"
msgstr "خرید"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__rating_ids
msgid "Ratings"
msgstr "رتبه‌ها"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Re-evaluate"
msgstr "بازنگری مجدد"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__linked_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__asset_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_form_asset_inherit
msgid "Related Assets"
msgstr "دارایی‌های مربوطه"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__related_purchase_value
msgid "Related Purchase Value"
msgstr "ارزش خرید مربوطه"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Reset to running"
msgstr "بر گرداندن به در حال اجرا"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_user_id
msgid "Responsible User"
msgstr "کاربر مسئول"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Resume"
msgstr "ازسرگیری"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Resume Depreciation"
msgstr "از سرگیری استهلاک"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"Reverse the depreciation entries posted in the future in order to modify the"
" depreciation"
msgstr ""
"به منظور اصلاح استهلاک، ورودی های استهلاک ارسال شده در آینده را معکوس کنید"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__open
msgid "Running"
msgstr "در حال اجرا"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطای تحویل پیامک"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__sale
msgid "Sale"
msgstr "فروش"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Save as Model"
msgstr "ذخیره به عنوان نمونه"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Save model"
msgstr "مدل ذخیره"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__select_invoice_line_id
msgid "Select Invoice Line"
msgstr "انتخاب سطر فاکتور"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Sell"
msgstr "فروش"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Draft"
msgstr "تبدیل به پیشنویس"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Running"
msgstr "تعیین به عنوان در حال اجرا"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Show all records which has next action date is before today"
msgstr "تمام رکوردهایی که تاریخ اقدام بعدی آن قبل از امروز است را نشان بده"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Some fields are missing %s"
msgstr "برخی فیلدها موجود نیستند %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Some required values are missing"
msgstr "بعضی مقادیر مورد نیاز موجود نیستند"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__prorata_date
msgid ""
"Starting date of the period used in the prorata calculation of the first "
"depreciation"
msgstr ""
"تاریخ شروع دوره که در محاسبه‌ی نسبی اولین استهلاک مورد استفاده قرار می‌گیرد"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__state
msgid "Status"
msgstr "وضعیت"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"وضعیت بر اساس فعالیت ها\n"
"سررسید: تاریخ سررسید گذشته است\n"
"امروز: تاریخ فعالیت امروز است\n"
"برنامه ریزی شده: فعالیت های آینده."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__linear
msgid "Straight Line"
msgstr "خط مستقیم"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__book_value
msgid ""
"Sum of the depreciable value, the salvage value and the book value of all "
"value increase items"
msgstr ""
"مجموع ارزش استهلاک پذیر، ارزش نجات یافته و ارزش دفتری همه اقلام افزایش می "
"یابد"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"کد کشور ISO در دو کاراکتر.\n"
"می توانید از این قسمت برای جستجوی سریع استفاده کنید."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"The account %(exp_acc)s has been credited by %(exp_delta)s, while the "
"account %(dep_acc)s has been debited by %(dep_delta)s. This corresponds to "
"%(move_count)s cancelled %(word)s:"
msgstr ""
"حساب %(exp_acc)s با %(exp_delta)s اعتبارسنجی شده است در حالی‌که حساب "
"%(dep_acc)s در حساب بدهی %(dep_delta)s قرار می‌گیرد. این موضوع مربوط به "
"%(word)s لغو شده‌ی %(move_count)s است.  "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_period
#: model:ir.model.fields,help:account_asset.field_asset_modify__method_period
msgid "The amount of time between two depreciations"
msgstr "میزان زمان بین دو استهلاک"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"The amount you have entered (%(entered_amount)s) does not match the Related "
"Purchase's value (%(purchase_value)s). Please make sure this is what you "
"want."
msgstr ""
"مبلغی که وارد کرده‌اید (%(entered_amount)s) با ارزش خرید مربوطه "
"(%(purchase_value)s) همخوانی ندارد. لطفاً اطمینان حاصل کنید که این مبلغ "
"همانی است که می‌خواهید."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__asset_id
msgid "The asset to be modified by this wizard"
msgstr "دارایی که باید تغییر داده شود توسط این ویزارد"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__children_ids
msgid "The children are the gains in value of this asset"
msgstr "زیر سطرها سود ارزش این دارایی هستند"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__invoice_ids
msgid ""
"The disposal invoice is needed in order to generate the closing journal "
"entry."
msgstr "فاکتور دارایی ها برای ایجاد سند اختتامیه مورد نیاز است."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "تعداد استهلاکهای مورد نیاز برای مستهلک شدن دارایی شما"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "The remaining value on the last depreciation line must be 0"
msgstr "مقدار باقیمانده در آخرین خط استهلاک باید 0 باشد"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__invoice_line_ids
msgid "There are multiple lines that could be the related to this asset"
msgstr "چندین خط وجود دارد که می تواند مربوط به این دارایی باشد"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"There are unposted depreciations prior to the selected operation date, "
"please deal with them first."
msgstr ""
"چند استهلاک ثبت نشده قبل از تاریخ انتخاب شده برای عملیات وجود دارند، لطفاً "
"به اولین آیتم بپردازید."

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/move_reversed/move_reversed.xml:0
msgid "This move has been reversed"
msgstr "این انتقال معکوس شده است"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Today Activities"
msgstr "فعالیت های امروز"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Total"
msgstr "مجموع"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciable_value
msgid "Total Depreciable Value"
msgstr "مجموع ارزش قابل استهلاک"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Turn as an asset"
msgstr "به دارایی تبدیل کن"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_type
msgid "Type of the account"
msgstr "نوع حساب"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع فعالیت استثنایی برای رکورد."

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Value at Import"
msgstr "ارزش در زمان ورود"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Value decrease for: %(asset)s"
msgstr "کاهش ارزش برای: %(asset)s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Value increase for: %(asset)s"
msgstr "افزایش ارزش برای: %(asset)s"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__warning_count_assets
msgid "Warning Count Assets"
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Warning for the Original Value of %s"
msgstr "هشدار برای ارزش اولیه‌ی %s"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__website_message_ids
msgid "Website Messages"
msgstr "پیام های وب سایت"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__website_message_ids
msgid "Website communication history"
msgstr "تاریخچه ارتباط با وبسایت"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"The 'On Hold' status can be set manually when you want to pause the depreciation of an asset for some time.\n"
"You can manually close an asset when the depreciation is over.\n"
"By cancelling an asset, all depreciation entries will be reversed"
msgstr ""
"زمانی که یک دارایی ایجاد می‌شود، وضعیت آن پیشنویس است.\n"
"اگر دارایی تأیید شود، وضعیت به «جاری» تبدیل شده و سطرهای استهلاک را می‌توان در حسابداری ثبت کرد.\n"
"زمانی که می‌خواهید استهلاک دارایی را برای مدتی متوقف کنید، وضعیت «توقف» را می‌توان به صورت دستی تعیین کرد.\n"
"زمانی که استهلاک به پایان برسد شما می‌توانید به صورت دستی یک دارایی را ببندید.\n"
"با لغو یک دارایی، تمام سندهای استهلاک معکوس می‌شوند"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__12
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__12
msgid "Years"
msgstr "سال"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/views/fields/properties/properties_field.js:0
msgid "You can add Property fields only on Assets with an Asset Model set."
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid ""
"You can't post an entry related to a draft asset. Please post the asset "
"before."
msgstr ""
"شما نمی‌توانید یک سند مرتبط با یک دارایی پیشنویس را ثبت کنید. لطفاً دارایی "
"را از قبل ثبت کنید."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "You can't re-evaluate the asset before the lock date."
msgstr "شما نمی‌توانید دارایی را پیش از تاریخ بستن مجدد ارزیابی کنید."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"You cannot add or remove bills when the asset is already running or closed."
msgstr ""

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "You cannot archive a record that is not closed"
msgstr "شما نمی توانید رکوردی که بسته نشده بایگانی کنید"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"You cannot automate the journal entry for an asset that has a running gross "
"increase. Please use 'Dispose' on the increase(s)."
msgstr ""
"شما نمی توانید برای یک ثبت سند روزنامه برای یک دارایی که یک افزایش ناخالص "
"جاری دارد، اتوماسیون بگذارید. لطفا از 'مستهلک کن' برای افزایش(ها) استفاده "
"کنید."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"You cannot create an asset from lines containing credit and debit on the "
"account or with a null amount"
msgstr ""
"شما نمی توانید دارایی را از خطوط حاوی بستانکاری و بدهی حساب یا با مبلغ صفر "
"ایجاد کنید"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "You cannot delete a document that is in %s state."
msgstr "شما نمی توانید یک سند که در حالت %s است را حذف کنید."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"You cannot delete an asset linked to posted entries.\n"
"You should either confirm the asset, then, sell or dispose of it, or cancel the linked journal entries."
msgstr ""
"شما نمی‌توانید یک دارایی مرتبط با سندهای ثبت شده را حذف کنید. \n"
"شما باید دارایی را تأیید کنید، بفروشید یا آن را واگذار کنید و یا اینکه سندهای دفتر روزنامه‌ مربوطه را لغو کنید."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "You cannot dispose of an asset before the lock date."
msgstr "شما نمی‌توانید پیش از تاریخ بستن حساب، دارایی را واگذار کنید."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "You cannot reset to draft an entry related to a posted asset"
msgstr ""
"شما نمی‌توانید سند مرتبط با یک دارایی ثبت شده را به حالت پیشنویس بازگردانید"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "You cannot resume at a date equal to or before the pause date"
msgstr ""
"شما نمی‌توانید در همان تاریخ توقف دارایی یا پیش از آن، روند را مجدد آغاز "
"کنید"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "You cannot select the same account as the Depreciation Account"
msgstr ""
"شما نمی‌توانید همان حساب استهلاک را به عنوان حساب موردنظر خود انتخاب کنید"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_balance
msgid "book_value"
msgstr "ارزش دفتری"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_date_from
#: model:account.report.column,name:account_asset.assets_report_depre_date_from
msgid "date from"
msgstr "از تاریخ"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_assets_date_to
#: model:account.report.column,name:account_asset.assets_report_depre_date_to
msgid "date to"
msgstr "تا تاریخ"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "depreciable)"
msgstr "قابل استهلاک)"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "e.g. Laptop iBook"
msgstr "برای مثال لب تاب آی بوک"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "entries"
msgstr "مورد"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "entry"
msgstr "مورد"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "gain"
msgstr "سود"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "gain/loss"
msgstr "سود/زیان"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "loss"
msgstr "زیان"
