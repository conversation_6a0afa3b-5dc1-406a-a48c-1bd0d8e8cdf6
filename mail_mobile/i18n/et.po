# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_mobile
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mail_mobile
#: model:ir.model.fields,help:mail_mobile.field_res_config_settings__disable_redirect_firebase_dynamic_link
msgid ""
"Check this if dynamic mobile-app detection links cause problems for your "
"installation. This will stop the automatic wrapping of links inside outbound"
" emails. The links will always open in a normal browser, even for users who "
"have the Android/iOS app installed."
msgstr ""

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__disable_redirect_firebase_dynamic_link
msgid "Disable link redirection to mobile app"
msgstr "Keela lingi ümbersuunamine mobiilirakendusel"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_discuss_channel
msgid "Discussion Channel"
msgstr "Sõnumite kanal"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_mail_thread
msgid "Email Thread"
msgstr "E-posti kirjavahetus"

#. module: mail_mobile
#: model:ir.model,name:mail_mobile.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid ""
"If disabled, you won't be able to open external URL's in the Android/iOS "
"mobile app (e.g. \"View Task\" button in email)."
msgstr ""
"Kui see on keelatud, ei ole Android/iOS mobiilirakenduses võimalik avada "
"väliseid URL-e (nt. \"Vaata ülesannet\" nupp e-mailis)."

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid "Mobile"
msgstr "Mobiil"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_partner__ocn_token
#: model:ir.model.fields,field_description:mail_mobile.field_res_users__ocn_token
msgid "OCN Token"
msgstr "OCN token"

#. module: mail_mobile
#: model:ir.model.fields,field_description:mail_mobile.field_res_config_settings__enable_ocn
msgid "Push Notifications"
msgstr "Märguanded"

#. module: mail_mobile
#: model_terms:ir.ui.view,arch_db:mail_mobile.res_config_settings_view_form
msgid "Receive notifications on Android and iOS application"
msgstr "Saa teavitusi Android ja iOS rakendustes"

#. module: mail_mobile
#: model:ir.model.fields,help:mail_mobile.field_res_partner__ocn_token
#: model:ir.model.fields,help:mail_mobile.field_res_users__ocn_token
msgid "Used for sending notification to registered devices"
msgstr ""
