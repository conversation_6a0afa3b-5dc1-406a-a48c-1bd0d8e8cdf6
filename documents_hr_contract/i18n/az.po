# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr_contract
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_hr_contract
#: model_terms:ir.ui.view,arch_db:documents_hr_contract.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Default tags</span>"
msgstr ""

#. module: documents_hr_contract
#: model:ir.model,name:documents_hr_contract.model_res_company
msgid "Companies"
msgstr "Şirkətlər"

#. module: documents_hr_contract
#: model:ir.model,name:documents_hr_contract.model_res_config_settings
msgid "Config Settings"
msgstr "Parametrləri Konfiqurasiya edin"

#. module: documents_hr_contract
#: model:ir.model.fields,field_description:documents_hr_contract.field_res_config_settings__documents_hr_contracts_tags
msgid "Contracts"
msgstr "Müqavilələr"

#. module: documents_hr_contract
#: model:documents.tag,name:documents_hr_contract.documents_hr_tag_contracts
msgid "Contracts (HR)"
msgstr ""

#. module: documents_hr_contract
#: model:ir.model.fields,field_description:documents_hr_contract.field_res_company__documents_hr_contracts_tags
msgid "Documents Hr Contracts Tags"
msgstr "İr Müqavilələri Etiketləri Sənədləri"

#. module: documents_hr_contract
#: model:ir.model,name:documents_hr_contract.model_hr_contract
msgid "Employee Contract"
msgstr "İşçilərin  Müqaviləsi"

#. module: documents_hr_contract
#: model:documents.tag,name:documents_hr_contract.document_tag_signature_request
msgid "Signature Request"
msgstr ""

#. module: documents_hr_contract
#: model:ir.model,name:documents_hr_contract.model_documents_tag
msgid "Tag"
msgstr "Etiket"

#. module: documents_hr_contract
#. odoo-python
#: code:addons/documents_hr_contract/models/documents_tag.py:0
msgid ""
"You cannot delete this tag as it is used to link employee contracts and "
"signatures."
msgstr ""
