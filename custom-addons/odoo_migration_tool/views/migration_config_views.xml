<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Migration Configuration Tree View -->
        <record id="migration_config_view_tree" model="ir.ui.view">
            <field name="name">migration.config.tree</field>
            <field name="model">migration.config</field>
            <field name="arch" type="xml">
                <list string="Migration Configurations">
                    <field name="name"/>
                    <field name="source_database"/>
                    <field name="dest_database"/>
                    <field name="source_connection_status" widget="badge" 
                           decoration-success="source_connection_status == 'connected'"
                           decoration-danger="source_connection_status == 'failed'"
                           decoration-muted="source_connection_status == 'not_tested'"/>
                    <field name="dest_connection_status" widget="badge"
                           decoration-success="dest_connection_status == 'connected'"
                           decoration-danger="dest_connection_status == 'failed'"
                           decoration-muted="dest_connection_status == 'not_tested'"/>
                    <field name="active" widget="boolean_toggle"/>
                </list>
            </field>
        </record>
        
        <!-- Migration Configuration Form View -->
        <record id="migration_config_view_form" model="ir.ui.view">
            <field name="name">migration.config.form</field>
            <field name="model">migration.config</field>
            <field name="arch" type="xml">
                <form string="Migration Configuration">
                    <header>
                        <button name="test_source_connection" string="Test Source Connection" 
                                type="object" class="btn-primary"/>
                        <button name="test_dest_connection" string="Test Destination Connection" 
                                type="object" class="btn-primary"/>
                        <button name="test_both_connections" string="Test Both Connections" 
                                type="object" class="btn-success"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="discover_source_models" string="Discover Models" 
                                    type="object" class="oe_stat_button" icon="fa-search"/>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Configuration Name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <field name="active"/>
                        </group>
                        
                        <notebook>
                            <page string="Source Database (Odoo 17)" name="source_db">
                                <group>
                                    <group string="Connection Details">
                                        <field name="source_host"/>
                                        <field name="source_port"/>
                                        <field name="source_database"/>
                                        <field name="source_username"/>
                                        <field name="source_password" password="True"/>
                                    </group>
                                    <group string="Connection Status">
                                        <field name="source_connection_status" widget="badge"
                                               decoration-success="source_connection_status == 'connected'"
                                               decoration-danger="source_connection_status == 'failed'"
                                               decoration-muted="source_connection_status == 'not_tested'"/>
                                        <field name="source_connection_message" readonly="1"/>
                                    </group>
                                </group>
                            </page>
                            
                            <page string="Destination Database (Odoo 18)" name="dest_db">
                                <group>
                                    <group string="Connection Details">
                                        <field name="dest_host"/>
                                        <field name="dest_port"/>
                                        <field name="dest_database"/>
                                        <field name="dest_username"/>
                                        <field name="dest_password" password="True"/>
                                    </group>
                                    <group string="Connection Status">
                                        <field name="dest_connection_status" widget="badge"
                                               decoration-success="dest_connection_status == 'connected'"
                                               decoration-danger="dest_connection_status == 'failed'"
                                               decoration-muted="dest_connection_status == 'not_tested'"/>
                                        <field name="dest_connection_message" readonly="1"/>
                                    </group>
                                </group>
                            </page>
                            
                            <page string="Migration Jobs" name="migration_jobs">
                                <field name="migration_job_ids">
                                    <list editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="migration_type"/>
                                        <field name="source_model"/>
                                        <field name="dest_model"/>
                                        <field name="state" widget="badge"
                                               decoration-info="state == 'draft'"
                                               decoration-success="state == 'ready'"
                                               decoration-warning="state == 'running'"
                                               decoration-success="state == 'completed'"
                                               decoration-danger="state == 'failed'"/>
                                        <field name="active" widget="boolean_toggle"/>
                                    </list>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Migration Configuration Search View -->
        <record id="migration_config_view_search" model="ir.ui.view">
            <field name="name">migration.config.search</field>
            <field name="model">migration.config</field>
            <field name="arch" type="xml">
                <search string="Migration Configurations">
                    <field name="name"/>
                    <field name="source_database"/>
                    <field name="dest_database"/>
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter string="Connected" name="connected" 
                            domain="[('source_connection_status', '=', 'connected'), ('dest_connection_status', '=', 'connected')]"/>
                    <filter string="Connection Issues" name="connection_issues" 
                            domain="['|', ('source_connection_status', '=', 'failed'), ('dest_connection_status', '=', 'failed')]"/>
                    <group expand="0" string="Group By">
                        <filter string="Source Connection Status" name="group_source_status" 
                                context="{'group_by': 'source_connection_status'}"/>
                        <filter string="Destination Connection Status" name="group_dest_status" 
                                context="{'group_by': 'dest_connection_status'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Migration Configuration Action -->
        <record id="migration_config_action" model="ir.actions.act_window">
            <field name="name">Migration Configurations</field>
            <field name="res_model">migration.config</field>
            <field name="view_mode">list,form</field>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first migration configuration!
                </p>
                <p>
                    Configure database connections for source (Odoo 17) and destination (Odoo 18) databases
                    to start migrating your data.
                </p>
            </field>
        </record>
        
    </data>
</odoo>
