<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Migration Mapping Tree View -->
        <record id="migration_mapping_view_tree" model="ir.ui.view">
            <field name="name">migration.mapping.tree</field>
            <field name="model">migration.mapping</field>
            <field name="arch" type="xml">
                <list string="Field Mappings" default_order="sequence" editable="bottom">
                    <field name="sequence" widget="handle"/>
                    <field name="job_id" readonly="1"/>
                    <field name="source_field"/>
                    <field name="dest_field"/>
                    <field name="field_type" readonly="1"/>
                    <field name="field_description" readonly="1"/>
                    <field name="mapping_type"/>
                    <field name="transform_expression" invisible="mapping_type != 'transform'"/>
                    <field name="lookup_model" invisible="mapping_type != 'lookup'"/>
                    <field name="lookup_field" invisible="mapping_type != 'lookup'"/>
                    <field name="lookup_return_field" invisible="mapping_type != 'lookup'"/>
                    <field name="default_value"/>
                    <field name="required" readonly="1" widget="boolean_toggle"/>
                    <field name="active" widget="boolean_toggle"/>
                    <button name="action_test_mapping" string="Test" type="object" 
                            icon="fa-play" title="Test Mapping"/>
                </list>
            </field>
        </record>
        
        <!-- Migration Mapping Form View -->
        <record id="migration_mapping_view_form" model="ir.ui.view">
            <field name="name">migration.mapping.form</field>
            <field name="model">migration.mapping</field>
            <field name="arch" type="xml">
                <form string="Field Mapping">
                    <header>
                        <button name="action_test_mapping" string="Test Mapping" type="object" 
                                class="btn-primary"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="source_field" readonly="1"/>
                                <span> → </span>
                                <field name="dest_field"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="job_id" readonly="1"/>
                                <field name="sequence"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="field_type" readonly="1"/>
                                <field name="field_description" readonly="1"/>
                                <field name="relation_model" readonly="1" invisible="not relation_model"/>
                                <field name="required" readonly="1"/>
                                <field name="readonly" readonly="1"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Mapping Configuration" name="mapping">
                                <group>
                                    <field name="mapping_type"/>
                                    <field name="default_value"/>
                                </group>
                                
                                <group string="Transform Configuration" invisible="mapping_type != 'transform'">
                                    <field name="transform_expression" nolabel="1" 
                                           placeholder="value.upper() if value else ''"
                                           help="Python expression to transform the value. Use 'value' as the source value."/>
                                </group>
                                
                                <group string="Lookup Configuration" invisible="mapping_type != 'lookup'">
                                    <field name="lookup_model" placeholder="res.partner"/>
                                    <field name="lookup_field" placeholder="name"/>
                                    <field name="lookup_return_field" placeholder="id"/>
                                </group>
                                
                                <group string="Validation">
                                    <field name="validate_required"/>
                                    <field name="validate_type"/>
                                </group>
                            </page>
                            
                            <page string="Statistics" name="statistics">
                                <group>
                                    <group>
                                        <field name="success_count" readonly="1"/>
                                        <field name="error_count" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="last_error" readonly="1" invisible="not last_error"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Migration Mapping Search View -->
        <record id="migration_mapping_view_search" model="ir.ui.view">
            <field name="name">migration.mapping.search</field>
            <field name="model">migration.mapping</field>
            <field name="arch" type="xml">
                <search string="Field Mappings">
                    <field name="source_field"/>
                    <field name="dest_field"/>
                    <field name="job_id"/>
                    <field name="field_type"/>
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter string="Direct Mapping" name="direct" domain="[('mapping_type', '=', 'direct')]"/>
                    <filter string="Transform" name="transform" domain="[('mapping_type', '=', 'transform')]"/>
                    <filter string="Lookup" name="lookup" domain="[('mapping_type', '=', 'lookup')]"/>
                    <filter string="Skip" name="skip" domain="[('mapping_type', '=', 'skip')]"/>
                    <separator/>
                    <filter string="Required Fields" name="required" domain="[('required', '=', True)]"/>
                    <filter string="With Errors" name="with_errors" domain="[('error_count', '>', 0)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Job" name="group_job" context="{'group_by': 'job_id'}"/>
                        <filter string="Mapping Type" name="group_mapping_type" context="{'group_by': 'mapping_type'}"/>
                        <filter string="Field Type" name="group_field_type" context="{'group_by': 'field_type'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Migration Mapping Action -->
        <record id="migration_mapping_action" model="ir.actions.act_window">
            <field name="name">Field Mappings</field>
            <field name="res_model">migration.mapping</field>
            <field name="view_mode">list,form</field>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No field mappings found!
                </p>
                <p>
                    Field mappings define how data is transformed when migrating from source to destination.
                    Use the "Discover Fields" button on migration jobs to automatically create mappings.
                </p>
            </field>
        </record>
        
        <!-- Migration Mapping Action from Job -->
        <record id="migration_mapping_action_from_job" model="ir.actions.act_window">
            <field name="name">Field Mappings</field>
            <field name="res_model">migration.mapping</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('job_id', '=', active_id)]</field>
            <field name="context">{'default_job_id': active_id, 'search_default_active': 1}</field>
        </record>
        
        <!-- Migration Mapping Test Form View -->
        <record id="migration_mapping_test_view_form" model="ir.ui.view">
            <field name="name">migration.mapping.test.form</field>
            <field name="model">migration.mapping.test</field>
            <field name="arch" type="xml">
                <form string="Test Field Mapping">
                    <sheet>
                        <group>
                            <field name="mapping_id" readonly="1"/>
                            <field name="test_value" placeholder="Enter test value"/>
                        </group>
                        
                        <group string="Result" invisible="not result_value and not error_message">
                            <field name="result_value" readonly="1" invisible="not result_value"/>
                            <field name="error_message" readonly="1" invisible="not error_message" 
                                   class="text-danger"/>
                        </group>
                    </sheet>
                    <footer>
                        <button name="action_test" string="Test" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>
        
    </data>
</odoo>
