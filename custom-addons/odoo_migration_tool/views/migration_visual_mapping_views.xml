<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Visual Field Mapping Form View -->
        <record id="migration_job_visual_mapping_view" model="ir.ui.view">
            <field name="name">migration.job.visual.mapping.form</field>
            <field name="model">migration.job</field>
            <field name="arch" type="xml">
                <form string="Visual Field Mapping" js_class="migration_visual_mapping_form">
                    <header>
                        <button name="action_discover_fields" string="Discover Fields" type="object" 
                                class="btn-primary"/>
                        <button name="action_count_records" string="Count Records" type="object" 
                                class="btn-secondary"/>
                        <button name="action_validate" string="Validate Job" type="object" 
                                class="btn-success" invisible="state != 'draft'"/>
                        <button name="action_run_migration" string="Run Migration" type="object" 
                                class="btn-warning" invisible="state != 'ready'"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,ready,running,completed"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="%(migration_job_action)d" string="Back to Jobs" 
                                    type="action" class="oe_stat_button" icon="fa-arrow-left"/>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                            <h3 class="text-muted">
                                <field name="source_model" readonly="1"/> → <field name="dest_model" readonly="1"/>
                            </h3>
                        </div>
                        
                        <group>
                            <group>
                                <field name="config_id" readonly="1"/>
                                <field name="migration_type" readonly="1"/>
                                <field name="batch_size" readonly="1"/>
                            </group>
                            <group>
                                <field name="total_records" readonly="1"/>
                                <field name="processed_records" readonly="1"/>
                                <field name="progress_percentage" widget="progressbar" readonly="1"/>
                            </group>
                        </group>
                        
                        <!-- Visual Field Mapping Widget -->
                        <div class="visual-mapping-container mt-4">
                            <widget name="field_mapping_widget" 
                                    job_id="id"
                                    readonly="state in ['running', 'completed']"/>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="quick-actions mt-4 p-3 bg-light rounded">
                            <h6>Quick Actions:</h6>
                            <div class="btn-group" role="group">
                                <button name="auto_map_same_names" string="Auto-map Same Names" 
                                        type="object" class="btn btn-sm btn-outline-primary"
                                        help="Automatically map fields with the same names"/>
                                <button name="auto_map_similar_names" string="Auto-map Similar Names" 
                                        type="object" class="btn btn-sm btn-outline-secondary"
                                        help="Automatically map fields with similar names"/>
                                <button name="clear_all_mappings" string="Clear All Mappings" 
                                        type="object" class="btn btn-sm btn-outline-danger"
                                        confirm="Are you sure you want to clear all mappings?"
                                        help="Remove all existing field mappings"/>
                            </div>
                        </div>
                        
                        <!-- Mapping Statistics -->
                        <div class="mapping-stats mt-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-primary">
                                                <field name="mapping_ids" widget="statinfo" string="Total Mappings"/>
                                            </h5>
                                            <p class="card-text">Total Mappings</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-success">
                                                <field name="direct_mapping_count"/>
                                            </h5>
                                            <p class="card-text">Direct Mappings</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-warning">
                                                <field name="transform_mapping_count"/>
                                            </h5>
                                            <p class="card-text">Transforms</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-info">
                                                <field name="lookup_mapping_count"/>
                                            </h5>
                                            <p class="card-text">Lookups</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Advanced Mapping Options -->
                        <div class="advanced-options mt-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <a data-bs-toggle="collapse" href="#advancedOptions" role="button" 
                                           aria-expanded="false" aria-controls="advancedOptions">
                                            Advanced Mapping Options
                                        </a>
                                    </h6>
                                </div>
                                <div class="collapse" id="advancedOptions">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Field Mapping Settings</h6>
                                                <field name="use_field_mapping"/>
                                                <field name="skip_existing"/>
                                                <field name="update_existing"/>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Error Handling</h6>
                                                <field name="continue_on_error"/>
                                            </div>
                                        </div>
                                        
                                        <div class="row mt-3">
                                            <div class="col-md-6">
                                                <h6>Date Filters</h6>
                                                <field name="date_from"/>
                                                <field name="date_to"/>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Domain Filter</h6>
                                                <field name="domain_filter" 
                                                       placeholder="[('field', '=', 'value')]"
                                                       help="Python domain filter for source records"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Action for Visual Mapping -->
        <record id="migration_job_visual_mapping_action" model="ir.actions.act_window">
            <field name="name">Visual Field Mapping</field>
            <field name="res_model">migration.job</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="migration_job_visual_mapping_view"/>
            <field name="target">current</field>
        </record>
        
    </data>
</odoo>
