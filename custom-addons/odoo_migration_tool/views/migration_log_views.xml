<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Migration Log Tree View -->
        <record id="migration_log_view_tree" model="ir.ui.view">
            <field name="name">migration.log.tree</field>
            <field name="model">migration.log</field>
            <field name="arch" type="xml">
                <list string="Migration Logs" default_order="create_date desc">
                    <field name="create_date"/>
                    <field name="job_id"/>
                    <field name="log_type" widget="badge"
                           decoration-info="log_type == 'info'"
                           decoration-warning="log_type == 'warning'"
                           decoration-danger="log_type == 'error'"
                           decoration-success="log_type == 'success'"
                           decoration-muted="log_type == 'debug'"/>
                    <field name="message"/>
                    <field name="source_record_id"/>
                    <field name="dest_record_id"/>
                    <field name="batch_number"/>
                    <field name="execution_time"/>
                    <button name="action_view_details" string="Details" type="object" 
                            icon="fa-eye" title="View Details"/>
                </list>
            </field>
        </record>
        
        <!-- Migration Log Form View -->
        <record id="migration_log_view_form" model="ir.ui.view">
            <field name="name">migration.log.form</field>
            <field name="model">migration.log</field>
            <field name="arch" type="xml">
                <form string="Migration Log" create="false" edit="false">
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="log_type" widget="badge"
                                       decoration-info="log_type == 'info'"
                                       decoration-warning="log_type == 'warning'"
                                       decoration-danger="log_type == 'error'"
                                       decoration-success="log_type == 'success'"
                                       decoration-muted="log_type == 'debug'"/>
                                <span class="ml-2">
                                    <field name="message"/>
                                </span>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="job_id"/>
                                <field name="config_id"/>
                                <field name="create_date"/>
                            </group>
                            <group>
                                <field name="source_record_id"/>
                                <field name="dest_record_id"/>
                                <field name="batch_number"/>
                                <field name="batch_size"/>
                                <field name="execution_time"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Details" name="details" invisible="not details">
                                <field name="details" readonly="1" nolabel="1"/>
                            </page>
                            
                            <page string="Record Data" name="record_data" invisible="not record_data">
                                <field name="record_data" readonly="1" nolabel="1"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Migration Log Search View -->
        <record id="migration_log_view_search" model="ir.ui.view">
            <field name="name">migration.log.search</field>
            <field name="model">migration.log</field>
            <field name="arch" type="xml">
                <search string="Migration Logs">
                    <field name="message"/>
                    <field name="job_id"/>
                    <field name="config_id"/>
                    <filter string="Info" name="info" domain="[('log_type', '=', 'info')]"/>
                    <filter string="Warning" name="warning" domain="[('log_type', '=', 'warning')]"/>
                    <filter string="Error" name="error" domain="[('log_type', '=', 'error')]"/>
                    <filter string="Success" name="success" domain="[('log_type', '=', 'success')]"/>
                    <filter string="Debug" name="debug" domain="[('log_type', '=', 'debug')]"/>
                    <separator/>
                    <filter string="Today" name="today" domain="[('create_date', '>=', datetime.datetime.now().replace(hour=0, minute=0, second=0))]"/>
                    <filter string="Last 7 Days" name="last_week" domain="[('create_date', '>=', (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                    <filter string="Last 30 Days" name="last_month" domain="[('create_date', '>=', (datetime.datetime.now() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Job" name="group_job" context="{'group_by': 'job_id'}"/>
                        <filter string="Log Type" name="group_log_type" context="{'group_by': 'log_type'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'create_date:day'}"/>
                        <filter string="Batch" name="group_batch" context="{'group_by': 'batch_number'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Migration Log Action -->
        <record id="migration_log_action" model="ir.actions.act_window">
            <field name="name">Migration Logs</field>
            <field name="res_model">migration.log</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No migration logs yet!
                </p>
                <p>
                    Migration logs will appear here when you run migration jobs.
                    They provide detailed information about the migration process.
                </p>
            </field>
        </record>
        
        <!-- Migration Log Action from Job -->
        <record id="migration_log_action_from_job" model="ir.actions.act_window">
            <field name="name">Migration Logs</field>
            <field name="res_model">migration.log</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('job_id', '=', active_id)]</field>
            <field name="context">{'default_job_id': active_id}</field>
        </record>
        
        <!-- Migration Log Summary Tree View -->
        <record id="migration_log_summary_view_tree" model="ir.ui.view">
            <field name="name">migration.log.summary.tree</field>
            <field name="model">migration.log.summary</field>
            <field name="arch" type="xml">
                <list string="Migration Log Summary" create="false" edit="false" delete="false">
                    <field name="job_name"/>
                    <field name="config_name"/>
                    <field name="total_logs"/>
                    <field name="info_count"/>
                    <field name="warning_count"/>
                    <field name="error_count"/>
                    <field name="success_count"/>
                    <field name="debug_count"/>
                    <field name="last_log_date"/>
                </list>
            </field>
        </record>
        
        <!-- Migration Log Summary Action -->
        <record id="migration_log_summary_action" model="ir.actions.act_window">
            <field name="name">Migration Log Summary</field>
            <field name="res_model">migration.log.summary</field>
            <field name="view_mode">list</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No migration logs summary available!
                </p>
                <p>
                    This view provides a summary of migration logs grouped by job.
                    Run some migration jobs to see the summary.
                </p>
            </field>
        </record>
        
    </data>
</odoo>
