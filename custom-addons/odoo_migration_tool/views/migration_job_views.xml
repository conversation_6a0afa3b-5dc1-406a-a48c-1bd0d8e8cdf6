<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Migration Job Tree View -->
        <record id="migration_job_view_tree" model="ir.ui.view">
            <field name="name">migration.job.tree</field>
            <field name="model">migration.job</field>
            <field name="arch" type="xml">
                <list string="Migration Jobs" default_order="sequence">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="config_id"/>
                    <field name="migration_type"/>
                    <field name="source_model"/>
                    <field name="dest_model"/>
                    <field name="total_records"/>
                    <field name="processed_records"/>
                    <field name="progress_percentage" widget="progressbar"/>
                    <field name="state" widget="badge"
                           decoration-info="state == 'draft'"
                           decoration-primary="state == 'ready'"
                           decoration-warning="state == 'running'"
                           decoration-success="state == 'completed'"
                           decoration-danger="state == 'failed'"
                           decoration-muted="state == 'cancelled'"/>
                    <field name="active" widget="boolean_toggle"/>
                </list>
            </field>
        </record>
        
        <!-- Migration Job Form View -->
        <record id="migration_job_view_form" model="ir.ui.view">
            <field name="name">migration.job.form</field>
            <field name="model">migration.job</field>
            <field name="arch" type="xml">
                <form string="Migration Job">
                    <header>
                        <button name="action_validate" string="Validate" type="object" 
                                class="btn-primary" invisible="state != 'draft'"/>
                        <button name="action_discover_fields" string="Discover Fields" type="object" 
                                class="btn-secondary" invisible="state not in ['draft', 'ready']"/>
                        <button name="action_count_records" string="Count Records" type="object" 
                                class="btn-secondary" invisible="state not in ['draft', 'ready']"/>
                        <button name="action_run_migration" string="Run Migration" type="object" 
                                class="btn-success" invisible="state != 'ready'"/>
                        <button name="action_reset_to_draft" string="Reset to Draft" type="object" 
                                class="btn-secondary" invisible="state in ['draft', 'running']"/>
                        <button name="action_cancel" string="Cancel" type="object" 
                                class="btn-danger" invisible="state not in ['draft', 'ready', 'running']"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,ready,running,completed"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="%(migration_mapping_action_from_job)d" string="Field Mappings" 
                                    type="action" class="oe_stat_button" icon="fa-exchange"
                                    context="{'search_default_job_id': active_id}">
                                <field name="mapping_ids" widget="statinfo" string="Mappings"/>
                            </button>
                            <button name="%(migration_log_action_from_job)d" string="Migration Logs" 
                                    type="action" class="oe_stat_button" icon="fa-list-alt"
                                    context="{'search_default_job_id': active_id}">
                                <field name="log_ids" widget="statinfo" string="Logs"/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Job Name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="config_id" required="1"/>
                                <field name="migration_type"/>
                                <field name="sequence"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="source_model"/>
                                <field name="dest_model"/>
                                <field name="batch_size"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Migration Settings" name="settings">
                                <group>
                                    <group string="Field Mapping">
                                        <field name="use_field_mapping"/>
                                    </group>
                                    <group string="Record Handling">
                                        <field name="skip_existing"/>
                                        <field name="update_existing"/>
                                        <field name="continue_on_error"/>
                                    </group>
                                </group>
                                
                                <group string="Filters">
                                    <group>
                                        <field name="date_from"/>
                                        <field name="date_to"/>
                                    </group>
                                    <group>
                                        <field name="domain_filter" placeholder="[('field', '=', 'value')]"/>
                                    </group>
                                </group>
                            </page>
                            
                            <page string="Progress" name="progress">
                                <group>
                                    <group string="Records">
                                        <field name="total_records" readonly="1"/>
                                        <field name="processed_records" readonly="1"/>
                                        <field name="success_records" readonly="1"/>
                                        <field name="failed_records" readonly="1"/>
                                        <field name="progress_percentage" widget="progressbar" readonly="1"/>
                                    </group>
                                    <group string="Timing">
                                        <field name="start_time" readonly="1"/>
                                        <field name="end_time" readonly="1"/>
                                        <field name="duration" readonly="1"/>
                                    </group>
                                </group>
                                
                                <group string="Error Information" invisible="not error_message">
                                    <field name="error_message" readonly="1" nolabel="1"/>
                                </group>
                            </page>
                            
                            <page string="Field Mappings" name="mappings">
                                <field name="mapping_ids">
                                    <list editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="source_field"/>
                                        <field name="dest_field"/>
                                        <field name="field_type" readonly="1"/>
                                        <field name="mapping_type"/>
                                        <field name="default_value"/>
                                        <field name="active" widget="boolean_toggle"/>
                                        <button name="action_test_mapping" string="Test" type="object" 
                                                icon="fa-play" title="Test Mapping"/>
                                    </list>
                                </field>
                            </page>
                            
                            <page string="Migration Logs" name="logs">
                                <field name="log_ids" readonly="1">
                                    <list>
                                        <field name="create_date"/>
                                        <field name="log_type" widget="badge"
                                               decoration-info="log_type == 'info'"
                                               decoration-warning="log_type == 'warning'"
                                               decoration-danger="log_type == 'error'"
                                               decoration-success="log_type == 'success'"
                                               decoration-muted="log_type == 'debug'"/>
                                        <field name="message"/>
                                        <field name="source_record_id"/>
                                        <field name="dest_record_id"/>
                                        <button name="action_view_details" string="Details" type="object" 
                                                icon="fa-eye" title="View Details"/>
                                    </list>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Migration Job Search View -->
        <record id="migration_job_view_search" model="ir.ui.view">
            <field name="name">migration.job.search</field>
            <field name="model">migration.job</field>
            <field name="arch" type="xml">
                <search string="Migration Jobs">
                    <field name="name"/>
                    <field name="config_id"/>
                    <field name="source_model"/>
                    <field name="dest_model"/>
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Ready" name="ready" domain="[('state', '=', 'ready')]"/>
                    <filter string="Running" name="running" domain="[('state', '=', 'running')]"/>
                    <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                    <filter string="Failed" name="failed" domain="[('state', '=', 'failed')]"/>
                    <separator/>
                    <filter string="Model Data" name="model_data" domain="[('migration_type', '=', 'model_data')]"/>
                    <filter string="Views" name="views" domain="[('migration_type', '=', 'views')]"/>
                    <filter string="Security" name="security" domain="[('migration_type', '=', 'security')]"/>
                    <group expand="0" string="Group By">
                        <filter string="Configuration" name="group_config" context="{'group_by': 'config_id'}"/>
                        <filter string="Migration Type" name="group_type" context="{'group_by': 'migration_type'}"/>
                        <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Migration Job Action -->
        <record id="migration_job_action" model="ir.actions.act_window">
            <field name="name">Migration Jobs</field>
            <field name="res_model">migration.job</field>
            <field name="view_mode">list,form</field>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first migration job!
                </p>
                <p>
                    Migration jobs define what data to migrate from your Odoo 17 to Odoo 18 database.
                    Configure the source and destination models, field mappings, and execution settings.
                </p>
            </field>
        </record>
        
    </data>
</odoo>
