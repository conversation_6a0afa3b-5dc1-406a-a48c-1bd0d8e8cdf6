<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Odoo Migration Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { text-align: center; margin-bottom: 40px; }
        .feature { margin: 20px 0; }
        .feature h3 { color: #875A7B; }
        .screenshot { text-align: center; margin: 20px 0; }
        .screenshot img { max-width: 100%; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Odoo Migration Tool</h1>
        <p>Complete solution for migrating data from Odoo 17 to Odoo 18</p>
    </div>

    <div class="feature">
        <h3>🔧 Database Configuration</h3>
        <p>Easy setup of source (Odoo 17) and destination (Odoo 18) database connections with connection testing.</p>
    </div>

    <div class="feature">
        <h3>🔄 Migration Jobs</h3>
        <p>Create and manage migration jobs for different types of data including models, views, security groups, and more.</p>
    </div>

    <div class="feature">
        <h3>🗺️ Field Mapping</h3>
        <p>Interactive interface to map fields between source and destination models with support for transformations and lookups.</p>
    </div>

    <div class="feature">
        <h3>📊 Progress Monitoring</h3>
        <p>Real-time progress tracking with detailed logging and error handling for comprehensive migration monitoring.</p>
    </div>

    <div class="feature">
        <h3>🛡️ Safety Features</h3>
        <p>Dry run mode, batch processing, rollback support, and comprehensive error handling to ensure safe migrations.</p>
    </div>

    <div class="feature">
        <h3>📈 Batch Processing</h3>
        <p>Efficient handling of large datasets with configurable batch sizes and progress tracking.</p>
    </div>

    <h2>Key Features</h2>
    <ul>
        <li>Database connection management with testing</li>
        <li>Automatic model and field discovery</li>
        <li>Flexible field mapping with transformations</li>
        <li>Support for various data types (models, views, security, etc.)</li>
        <li>Real-time progress monitoring</li>
        <li>Comprehensive logging and error handling</li>
        <li>Dry run mode for testing</li>
        <li>Batch processing for performance</li>
        <li>User-friendly interface</li>
        <li>Role-based access control</li>
    </ul>

    <h2>Installation</h2>
    <ol>
        <li>Copy the module to your Odoo addons directory</li>
        <li>Update the app list</li>
        <li>Install the "Odoo Migration Tool" module</li>
        <li>Configure database connections</li>
        <li>Create migration jobs and start migrating!</li>
    </ol>

    <h2>Support</h2>
    <p>For support and documentation, please refer to the module documentation or contact the development team.</p>
</body>
</html>
