<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="odoo_migration_tool.FieldMappingWidget">
        <div class="o_field_mapping_widget">
            <div class="mapping-header d-flex justify-content-between align-items-center mb-3">
                <h4>Visual Field Mapping</h4>
                <div class="mapping-controls">
                    <button class="btn btn-sm btn-secondary me-2" t-on-click="loadFields">
                        <i class="fa fa-refresh"/> Refresh Fields
                    </button>
                    <button class="btn btn-sm btn-primary" t-on-click="loadMappings">
                        <i class="fa fa-sync"/> Reload Mappings
                    </button>
                </div>
            </div>
            
            <div class="mapping-legend mb-3">
                <small class="text-muted">
                    <span class="me-3">
                        <span class="legend-line direct"></span> Direct Mapping
                    </span>
                    <span class="me-3">
                        <span class="legend-line transform"></span> Transform
                    </span>
                    <span class="me-3">
                        <span class="legend-line lookup"></span> Lookup
                    </span>
                    <span class="me-3">
                        <span class="legend-line skip"></span> Skip
                    </span>
                </small>
            </div>
            
            <div class="mapping-container position-relative">
                <div class="row">
                    <!-- Source Fields Column -->
                    <div class="col-5">
                        <div class="field-panel source-panel">
                            <div class="panel-header bg-primary text-white p-2">
                                <h6 class="mb-0">
                                    <i class="fa fa-database me-2"/>
                                    Source Fields (Odoo 17)
                                </h6>
                            </div>
                            <div class="field-list" t-ref="sourceList">
                                <div t-foreach="state.sourceFields" t-as="field" t-key="field.name"
                                     class="field-item d-flex align-items-center p-2 border-bottom"
                                     t-att-data-field-name="field.name"
                                     t-att-title="field.description">
                                    <i t-att-class="'fa ' + getFieldIcon(field.type) + ' me-2 text-muted'"/>
                                    <div class="field-info flex-grow-1">
                                        <div class="field-name fw-bold" t-esc="field.name"/>
                                        <small class="field-type text-muted" t-esc="field.type"/>
                                    </div>
                                    <div class="field-badges">
                                        <span t-if="field.required" class="badge badge-sm bg-warning">Required</span>
                                        <span t-if="field.readonly" class="badge badge-sm bg-secondary">Readonly</span>
                                    </div>
                                </div>
                                <div t-if="!state.sourceFields.length" class="text-center p-4 text-muted">
                                    <i class="fa fa-info-circle fa-2x mb-2"/>
                                    <div>No source fields available</div>
                                    <small>Click "Refresh Fields" to load fields</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Connection Canvas -->
                    <div class="col-2 p-0">
                        <canvas t-ref="canvas" class="connection-canvas w-100 h-100"/>
                    </div>
                    
                    <!-- Destination Fields Column -->
                    <div class="col-5">
                        <div class="field-panel dest-panel">
                            <div class="panel-header bg-success text-white p-2">
                                <h6 class="mb-0">
                                    <i class="fa fa-database me-2"/>
                                    Destination Fields (Odoo 18)
                                </h6>
                            </div>
                            <div class="field-list" t-ref="destList">
                                <div t-foreach="state.destFields" t-as="field" t-key="field.name"
                                     class="field-item d-flex align-items-center p-2 border-bottom"
                                     t-att-data-field-name="field.name"
                                     t-att-title="field.description">
                                    <div class="field-badges me-2">
                                        <span t-if="field.required" class="badge badge-sm bg-warning">Required</span>
                                        <span t-if="field.readonly" class="badge badge-sm bg-secondary">Readonly</span>
                                    </div>
                                    <div class="field-info flex-grow-1">
                                        <div class="field-name fw-bold" t-esc="field.name"/>
                                        <small class="field-type text-muted" t-esc="field.type"/>
                                    </div>
                                    <i t-att-class="'fa ' + getFieldIcon(field.type) + ' ms-2 text-muted'"/>
                                </div>
                                <div t-if="!state.destFields.length" class="text-center p-4 text-muted">
                                    <i class="fa fa-info-circle fa-2x mb-2"/>
                                    <div>No destination fields available</div>
                                    <small>Click "Refresh Fields" to load fields</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Connection Instructions -->
                <div class="mapping-instructions mt-3 p-3 bg-light rounded">
                    <h6>How to create mappings:</h6>
                    <ul class="mb-0 small">
                        <li>Click on a source field (left side) to start a connection</li>
                        <li>Click on a destination field (right side) to complete the connection</li>
                        <li>Click on the canvas or press Escape to cancel a connection</li>
                        <li>Right-click on a connection line to modify or remove the mapping</li>
                    </ul>
                </div>
                
                <!-- Current Mappings List -->
                <div class="current-mappings mt-3" t-if="state.mappings.length">
                    <h6>Current Mappings:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Source Field</th>
                                    <th>Destination Field</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="state.mappings" t-as="mapping" t-key="mapping.id">
                                    <td t-esc="mapping.source_field"/>
                                    <td t-esc="mapping.dest_field"/>
                                    <td>
                                        <span t-att-class="'badge bg-' + (mapping.mapping_type === 'direct' ? 'success' : mapping.mapping_type === 'transform' ? 'warning' : mapping.mapping_type === 'lookup' ? 'info' : 'danger')"
                                              t-esc="mapping.mapping_type"/>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-danger" 
                                                t-on-click="() => this.removeMapping(mapping)"
                                                title="Remove mapping">
                                            <i class="fa fa-trash"/>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </t>

</templates>
