<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="odoo_migration_tool.VisualMappingWidget">
        <div class="o_visual_mapping_widget">
            <!-- Header -->
            <div class="mapping-header d-flex justify-content-between align-items-center mb-3">
                <h5><i class="fa fa-exchange me-2"/>Visual Field Mapping - Talend Style</h5>
                <div class="mapping-controls">
                    <button class="btn btn-sm btn-outline-primary" t-on-click="refreshData">
                        <i class="fa fa-refresh me-1"/>Refresh
                    </button>
                </div>
            </div>

            <!-- Loading indicator -->
            <div t-if="state.loading" class="text-center p-4">
                <i class="fa fa-spinner fa-spin fa-2x"/>
                <div class="mt-2">Loading field mapping data...</div>
            </div>

            <!-- Main mapping interface -->
            <div t-if="!state.loading" class="mapping-container">
                <div class="row">
                    <!-- Source Fields -->
                    <div class="col-5">
                        <div class="field-panel source-panel">
                            <div class="panel-header bg-primary text-white p-2">
                                <h6 class="mb-0">
                                    <i class="fa fa-database me-2"/>Source Fields (Odoo 17)
                                </h6>
                                <small t-esc="state.sourceFields.length"/> fields
                            </div>
                            <div class="field-list" t-ref="sourceList">
                                <div t-foreach="state.sourceFields" t-as="field" t-key="field.name"
                                     class="field-item"
                                     t-att-class="{
                                         'selected': state.selectedSourceField === field.name,
                                         'mapped': state.mappings.some(m => m.source_field === field.name)
                                     }"
                                     t-att-data-field="field.name"
                                     t-on-click="() => this.onSourceFieldClick(field.name)">
                                    <div class="d-flex align-items-center p-2">
                                        <i t-att-class="'fa ' + getFieldIcon(field.type) + ' me-2 text-muted'"/>
                                        <div class="field-info flex-grow-1">
                                            <div class="field-name fw-bold" t-esc="field.name"/>
                                            <small class="field-type text-muted" t-esc="field.type"/>
                                        </div>
                                        <div class="field-badges">
                                            <span t-if="field.required" class="badge badge-sm bg-warning">Req</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div t-if="!state.sourceFields.length" class="text-center p-4 text-muted">
                                    <i class="fa fa-info-circle fa-2x mb-2"/>
                                    <div>No source fields available</div>
                                    <small>Click "Discover Fields" in the job to load fields</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Connection Canvas -->
                    <div class="col-2 p-0 position-relative">
                        <canvas t-ref="canvas" class="connection-canvas w-100 h-100"/>
                        <div class="connection-instructions">
                            <small class="text-muted">
                                Click source field, then destination field to connect
                            </small>
                        </div>
                    </div>

                    <!-- Destination Fields -->
                    <div class="col-5">
                        <div class="field-panel dest-panel">
                            <div class="panel-header bg-success text-white p-2">
                                <h6 class="mb-0">
                                    <i class="fa fa-database me-2"/>Destination Fields (Odoo 18)
                                </h6>
                                <small t-esc="state.destFields.length"/> fields
                            </div>
                            <div class="field-list" t-ref="destList">
                                <div t-foreach="state.destFields" t-as="field" t-key="field.name"
                                     class="field-item"
                                     t-att-class="{
                                         'mapped': state.mappings.some(m => m.dest_field === field.name)
                                     }"
                                     t-att-data-field="field.name"
                                     t-on-click="() => this.onDestFieldClick(field.name)">
                                    <div class="d-flex align-items-center p-2">
                                        <div class="field-badges me-2">
                                            <span t-if="field.required" class="badge badge-sm bg-warning">Req</span>
                                        </div>
                                        <div class="field-info flex-grow-1">
                                            <div class="field-name fw-bold" t-esc="field.name"/>
                                            <small class="field-type text-muted" t-esc="field.type"/>
                                        </div>
                                        <i t-att-class="'fa ' + getFieldIcon(field.type) + ' ms-2 text-muted'"/>
                                    </div>
                                </div>
                                
                                <div t-if="!state.destFields.length" class="text-center p-4 text-muted">
                                    <i class="fa fa-info-circle fa-2x mb-2"/>
                                    <div>No destination fields available</div>
                                    <small>Click "Discover Fields" in the job to load fields</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Connection Status -->
                <div t-if="state.isConnecting" class="alert alert-info mt-3">
                    <i class="fa fa-link me-2"/>
                    Connecting from <strong t-esc="state.selectedSourceField"/>. 
                    Click a destination field to complete the connection, or click the source field again to cancel.
                </div>

                <!-- Current Mappings -->
                <div t-if="state.mappings.length" class="current-mappings mt-3">
                    <h6>Current Mappings (<span t-esc="state.mappings.length"/>)</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Source Field</th>
                                    <th>Destination Field</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="state.mappings" t-as="mapping" t-key="mapping.id">
                                    <td>
                                        <i t-att-class="'fa fa-circle me-1 text-' + (mapping.mapping_type === 'direct' ? 'success' : mapping.mapping_type === 'transform' ? 'warning' : 'info')"/>
                                        <span t-esc="mapping.source_field"/>
                                    </td>
                                    <td t-esc="mapping.dest_field"/>
                                    <td>
                                        <span t-att-class="'badge bg-' + (mapping.mapping_type === 'direct' ? 'success' : mapping.mapping_type === 'transform' ? 'warning' : 'info')"
                                              t-esc="mapping.mapping_type"/>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-danger" 
                                                t-on-click="() => this.removeMapping(mapping)"
                                                title="Remove mapping">
                                            <i class="fa fa-trash"/>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="mapping-instructions mt-3 p-3 bg-light rounded">
                    <h6><i class="fa fa-info-circle me-2"/>How to create mappings:</h6>
                    <ul class="mb-0 small">
                        <li><strong>Step 1:</strong> Click on a source field (left panel) to start a connection</li>
                        <li><strong>Step 2:</strong> Click on a destination field (right panel) to complete the connection</li>
                        <li><strong>Cancel:</strong> Click the selected source field again to cancel</li>
                        <li><strong>Visual:</strong> Connected fields show curved lines with arrows</li>
                        <li><strong>Colors:</strong> Green = Direct, Yellow = Transform, Blue = Lookup</li>
                    </ul>
                </div>
            </div>
        </div>
    </t>

</templates>
