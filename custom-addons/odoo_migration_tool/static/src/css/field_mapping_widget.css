/* Field Mapping Widget Styles */

.o_field_mapping_widget {
    min-height: 600px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.mapping-container {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
}

.field-panel {
    height: 500px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.panel-header {
    border-radius: 6px 6px 0 0;
    font-weight: 600;
}

.field-list {
    height: calc(100% - 40px);
    overflow-y: auto;
    border-radius: 0 0 6px 6px;
}

.field-item {
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
    margin: 1px;
    border-radius: 4px;
}

.field-item:hover {
    background: #e3f2fd;
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.field-item.selected {
    background: #2196f3;
    color: white;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.field-item.selected .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

.field-item.mapped {
    border-left: 4px solid #28a745;
}

.field-name {
    font-size: 14px;
    line-height: 1.2;
}

.field-type {
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.field-badges .badge {
    font-size: 9px;
    padding: 2px 6px;
    margin-left: 4px;
}

.connection-canvas {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: auto;
    z-index: 10;
}

.mapping-legend {
    display: flex;
    align-items: center;
    gap: 15px;
}

.legend-line {
    display: inline-block;
    width: 20px;
    height: 3px;
    margin-right: 5px;
    border-radius: 2px;
}

.legend-line.direct {
    background: #28a745;
}

.legend-line.transform {
    background: #ffc107;
}

.legend-line.lookup {
    background: #17a2b8;
}

.legend-line.skip {
    background: #dc3545;
}

.mapping-instructions {
    border: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.mapping-instructions h6 {
    color: #495057;
    margin-bottom: 10px;
}

.mapping-instructions ul {
    color: #6c757d;
}

.current-mappings {
    background: white;
    border-radius: 6px;
    padding: 15px;
    border: 1px solid #dee2e6;
}

.current-mappings h6 {
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
    margin-bottom: 15px;
}

/* Source panel specific styles */
.source-panel .field-item:hover {
    border-left: 4px solid #2196f3;
}

.source-panel .field-item.selected {
    border-left: 4px solid #fff;
}

/* Destination panel specific styles */
.dest-panel .field-item:hover {
    border-right: 4px solid #4caf50;
}

.dest-panel .field-item.selected {
    background: #4caf50;
    border-right: 4px solid #fff;
}

/* Connection states */
.field-item.connecting {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Responsive design */
@media (max-width: 768px) {
    .mapping-container .row {
        flex-direction: column;
    }
    
    .mapping-container .col-5,
    .mapping-container .col-2 {
        width: 100%;
        max-width: 100%;
    }
    
    .connection-canvas {
        height: 100px;
        position: relative;
    }
    
    .field-panel {
        height: 300px;
        margin-bottom: 10px;
    }
}

/* Scrollbar styling */
.field-list::-webkit-scrollbar {
    width: 6px;
}

.field-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.field-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.field-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Button hover effects */
.mapping-controls .btn {
    transition: all 0.2s ease;
}

.mapping-controls .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Table styling */
.current-mappings table {
    border-radius: 6px;
    overflow: hidden;
}

.current-mappings thead th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.current-mappings tbody td {
    border: none;
    border-bottom: 1px solid #f1f1f1;
    vertical-align: middle;
}

.current-mappings tbody tr:hover {
    background: #f8f9fa;
}

/* Loading states */
.field-list.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.field-list.loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
