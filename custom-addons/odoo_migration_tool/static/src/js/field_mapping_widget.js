/** @odoo-module **/

import { Component, useState, useRef, onMounted, onWillUnmount } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";
import { _t } from "@web/core/l10n/translation";

class FieldMappingWidget extends Component {
    static template = "odoo_migration_tool.FieldMappingWidget";
    static props = {
        jobId: { type: Number, optional: true },
        sourceFields: { type: Array, optional: true },
        destFields: { type: Array, optional: true },
        mappings: { type: Array, optional: true },
        readonly: { type: Boolean, optional: true },
    };

    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");
        this.canvasRef = useRef("canvas");
        this.sourceListRef = useRef("sourceList");
        this.destListRef = useRef("destList");
        
        this.state = useState({
            sourceFields: this.props.sourceFields || [],
            destFields: this.props.destFields || [],
            mappings: this.props.mappings || [],
            selectedSourceField: null,
            selectedDestField: null,
            isConnecting: false,
            connections: [],
        });

        onMounted(() => {
            this.setupCanvas();
            this.loadFields();
            this.loadMappings();
            this.setupEventListeners();
        });

        onWillUnmount(() => {
            this.removeEventListeners();
        });
    }

    setupCanvas() {
        const canvas = this.canvasRef.el;
        const container = canvas.parentElement;
        
        // Set canvas size to match container
        const resizeCanvas = () => {
            canvas.width = container.offsetWidth;
            canvas.height = container.offsetHeight;
            this.redrawConnections();
        };
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        this.resizeHandler = resizeCanvas;
    }

    setupEventListeners() {
        // Add click listeners for field items
        this.sourceListRef.el.addEventListener('click', this.onSourceFieldClick.bind(this));
        this.destListRef.el.addEventListener('click', this.onDestFieldClick.bind(this));
        
        // Add mouse move listener for drawing connections
        this.canvasRef.el.addEventListener('mousemove', this.onMouseMove.bind(this));
        this.canvasRef.el.addEventListener('click', this.onCanvasClick.bind(this));
    }

    removeEventListeners() {
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
        }
    }

    async loadFields() {
        if (!this.props.jobId) return;
        
        try {
            // Load source fields
            const sourceFields = await this.orm.call(
                'migration.job',
                'get_source_fields',
                [this.props.jobId]
            );
            
            // Load destination fields
            const destFields = await this.orm.call(
                'migration.job',
                'get_dest_fields',
                [this.props.jobId]
            );
            
            this.state.sourceFields = sourceFields;
            this.state.destFields = destFields;
            
        } catch (error) {
            console.error('Error loading fields:', error);
            this.notification.add(_t('Error loading fields'), { type: 'danger' });
        }
    }

    async loadMappings() {
        if (!this.props.jobId) return;
        
        try {
            const mappings = await this.orm.searchRead(
                'migration.mapping',
                [['job_id', '=', this.props.jobId], ['active', '=', true]],
                ['source_field', 'dest_field', 'mapping_type']
            );
            
            this.state.mappings = mappings;
            this.updateConnections();
            
        } catch (error) {
            console.error('Error loading mappings:', error);
        }
    }

    onSourceFieldClick(event) {
        const fieldItem = event.target.closest('.field-item');
        if (!fieldItem) return;
        
        const fieldName = fieldItem.dataset.fieldName;
        
        if (this.state.isConnecting && this.state.selectedSourceField === fieldName) {
            // Cancel connection
            this.cancelConnection();
        } else {
            // Start new connection
            this.startConnection(fieldName, 'source');
        }
    }

    onDestFieldClick(event) {
        const fieldItem = event.target.closest('.field-item');
        if (!fieldItem) return;
        
        const fieldName = fieldItem.dataset.fieldName;
        
        if (this.state.isConnecting && this.state.selectedSourceField) {
            // Complete connection
            this.completeConnection(fieldName);
        } else {
            // Start connection from destination
            this.startConnection(fieldName, 'dest');
        }
    }

    startConnection(fieldName, side) {
        this.state.isConnecting = true;
        
        if (side === 'source') {
            this.state.selectedSourceField = fieldName;
            this.state.selectedDestField = null;
        } else {
            this.state.selectedDestField = fieldName;
            this.state.selectedSourceField = null;
        }
        
        // Update visual state
        this.updateFieldSelection();
    }

    async completeConnection(destFieldName) {
        if (!this.state.selectedSourceField) return;
        
        try {
            // Check if mapping already exists
            const existingMapping = this.state.mappings.find(
                m => m.source_field === this.state.selectedSourceField
            );
            
            if (existingMapping) {
                // Update existing mapping
                await this.orm.write(
                    'migration.mapping',
                    [existingMapping.id],
                    { dest_field: destFieldName }
                );
            } else {
                // Create new mapping
                await this.orm.create(
                    'migration.mapping',
                    [{
                        job_id: this.props.jobId,
                        source_field: this.state.selectedSourceField,
                        dest_field: destFieldName,
                        mapping_type: 'direct',
                        active: true,
                    }]
                );
            }
            
            // Reload mappings
            await this.loadMappings();
            
            this.notification.add(
                _t('Field mapping created: %s → %s', this.state.selectedSourceField, destFieldName),
                { type: 'success' }
            );
            
        } catch (error) {
            console.error('Error creating mapping:', error);
            this.notification.add(_t('Error creating mapping'), { type: 'danger' });
        }
        
        this.cancelConnection();
    }

    cancelConnection() {
        this.state.isConnecting = false;
        this.state.selectedSourceField = null;
        this.state.selectedDestField = null;
        this.updateFieldSelection();
    }

    onMouseMove(event) {
        if (!this.state.isConnecting || !this.state.selectedSourceField) return;
        
        this.drawTemporaryConnection(event);
    }

    onCanvasClick(event) {
        if (this.state.isConnecting) {
            this.cancelConnection();
        }
    }

    updateFieldSelection() {
        // Update CSS classes for selected fields
        const sourceItems = this.sourceListRef.el.querySelectorAll('.field-item');
        const destItems = this.destListRef.el.querySelectorAll('.field-item');
        
        sourceItems.forEach(item => {
            item.classList.toggle(
                'selected',
                item.dataset.fieldName === this.state.selectedSourceField
            );
        });
        
        destItems.forEach(item => {
            item.classList.toggle(
                'selected',
                item.dataset.fieldName === this.state.selectedDestField
            );
        });
    }

    updateConnections() {
        const connections = [];
        
        this.state.mappings.forEach(mapping => {
            const sourceEl = this.sourceListRef.el.querySelector(
                `[data-field-name="${mapping.source_field}"]`
            );
            const destEl = this.destListRef.el.querySelector(
                `[data-field-name="${mapping.dest_field}"]`
            );
            
            if (sourceEl && destEl) {
                connections.push({
                    source: sourceEl,
                    dest: destEl,
                    mapping: mapping,
                });
            }
        });
        
        this.state.connections = connections;
        this.redrawConnections();
    }

    redrawConnections() {
        const canvas = this.canvasRef.el;
        const ctx = canvas.getContext('2d');
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw connections
        this.state.connections.forEach(connection => {
            this.drawConnection(ctx, connection.source, connection.dest, connection.mapping);
        });
    }

    drawConnection(ctx, sourceEl, destEl, mapping) {
        const sourceRect = sourceEl.getBoundingClientRect();
        const destRect = destEl.getBoundingClientRect();
        const canvasRect = this.canvasRef.el.getBoundingClientRect();
        
        const startX = sourceRect.right - canvasRect.left;
        const startY = sourceRect.top + sourceRect.height / 2 - canvasRect.top;
        const endX = destRect.left - canvasRect.left;
        const endY = destRect.top + destRect.height / 2 - canvasRect.top;
        
        // Set line style based on mapping type
        ctx.strokeStyle = this.getMappingColor(mapping.mapping_type);
        ctx.lineWidth = 2;
        
        // Draw curved line
        ctx.beginPath();
        ctx.moveTo(startX, startY);
        
        const controlX1 = startX + (endX - startX) / 3;
        const controlX2 = startX + 2 * (endX - startX) / 3;
        
        ctx.bezierCurveTo(controlX1, startY, controlX2, endY, endX, endY);
        ctx.stroke();
        
        // Draw arrow at the end
        this.drawArrow(ctx, controlX2, endY, endX, endY);
    }

    drawTemporaryConnection(event) {
        if (!this.state.selectedSourceField) return;
        
        const canvas = this.canvasRef.el;
        const ctx = canvas.getContext('2d');
        const canvasRect = canvas.getBoundingClientRect();
        
        // Redraw existing connections
        this.redrawConnections();
        
        // Draw temporary line
        const sourceEl = this.sourceListRef.el.querySelector(
            `[data-field-name="${this.state.selectedSourceField}"]`
        );
        
        if (sourceEl) {
            const sourceRect = sourceEl.getBoundingClientRect();
            const startX = sourceRect.right - canvasRect.left;
            const startY = sourceRect.top + sourceRect.height / 2 - canvasRect.top;
            const endX = event.clientX - canvasRect.left;
            const endY = event.clientY - canvasRect.top;
            
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();
            
            ctx.setLineDash([]);
        }
    }

    drawArrow(ctx, fromX, fromY, toX, toY) {
        const headlen = 10;
        const angle = Math.atan2(toY - fromY, toX - fromX);
        
        ctx.beginPath();
        ctx.moveTo(toX, toY);
        ctx.lineTo(
            toX - headlen * Math.cos(angle - Math.PI / 6),
            toY - headlen * Math.sin(angle - Math.PI / 6)
        );
        ctx.moveTo(toX, toY);
        ctx.lineTo(
            toX - headlen * Math.cos(angle + Math.PI / 6),
            toY - headlen * Math.sin(angle + Math.PI / 6)
        );
        ctx.stroke();
    }

    getMappingColor(mappingType) {
        const colors = {
            'direct': '#28a745',
            'transform': '#ffc107',
            'lookup': '#17a2b8',
            'skip': '#dc3545',
        };
        return colors[mappingType] || '#6c757d';
    }

    async removeMapping(mapping) {
        try {
            await this.orm.unlink('migration.mapping', [mapping.id]);
            await this.loadMappings();
            
            this.notification.add(
                _t('Mapping removed: %s → %s', mapping.source_field, mapping.dest_field),
                { type: 'info' }
            );
        } catch (error) {
            console.error('Error removing mapping:', error);
            this.notification.add(_t('Error removing mapping'), { type: 'danger' });
        }
    }

    getFieldIcon(fieldType) {
        const icons = {
            'char': 'fa-font',
            'text': 'fa-align-left',
            'integer': 'fa-hashtag',
            'float': 'fa-calculator',
            'boolean': 'fa-check-square',
            'date': 'fa-calendar',
            'datetime': 'fa-clock-o',
            'many2one': 'fa-link',
            'one2many': 'fa-list',
            'many2many': 'fa-tags',
            'selection': 'fa-list-ul',
            'binary': 'fa-file',
        };
        return icons[fieldType] || 'fa-question';
    }
}

// Register the widget
registry.category("view_widgets").add("field_mapping_widget", FieldMappingWidget);
