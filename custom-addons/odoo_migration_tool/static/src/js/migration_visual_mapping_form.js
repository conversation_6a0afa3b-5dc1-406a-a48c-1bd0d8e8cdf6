/** @odoo-module **/

import { FormController } from "@web/views/form/form_controller";
import { registry } from "@web/core/registry";

export class MigrationVisualMappingFormController extends FormController {
    setup() {
        super.setup();
    }
}

export const migrationVisualMappingFormView = {
    ...registry.category("views").get("form"),
    Controller: MigrationVisualMappingFormController,
};

registry.category("views").add("migration_visual_mapping_form", migrationVisualMappingFormView);
