/** @odoo-module **/

import { Component, useState, useRef, onMounted, onWillUnmount } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";
import { _t } from "@web/core/l10n/translation";
import { standardWidgetProps } from "@web/views/widgets/standard_widget_props";

export class VisualMappingWidget extends Component {
    static template = "odoo_migration_tool.VisualMappingWidget";
    static props = {
        ...standardWidgetProps,
    };

    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");
        this.canvasRef = useRef("canvas");
        this.sourceListRef = useRef("sourceList");
        this.destListRef = useRef("destList");
        
        this.jobId = this.props.record?.data?.id;
        
        this.state = useState({
            sourceFields: [],
            destFields: [],
            mappings: [],
            selectedSourceField: null,
            isConnecting: false,
            loading: false,
        });

        onMounted(() => {
            if (this.jobId) {
                this.loadData();
                this.setupCanvas();
            }
        });

        onWillUnmount(() => {
            if (this.resizeHandler) {
                window.removeEventListener('resize', this.resizeHandler);
            }
        });
    }

    async loadData() {
        this.state.loading = true;
        try {
            await Promise.all([
                this.loadSourceFields(),
                this.loadDestFields(),
                this.loadMappings()
            ]);
            this.drawConnections();
        } catch (error) {
            console.error('Error loading data:', error);
            this.notification.add(_t('Error loading field mapping data'), { type: 'danger' });
        } finally {
            this.state.loading = false;
        }
    }

    async loadSourceFields() {
        const fields = await this.orm.call('migration.job', 'get_source_fields', [this.jobId]);
        this.state.sourceFields = fields;
    }

    async loadDestFields() {
        const fields = await this.orm.call('migration.job', 'get_dest_fields', [this.jobId]);
        this.state.destFields = fields;
    }

    async loadMappings() {
        const mappings = await this.orm.searchRead(
            'migration.mapping',
            [['job_id', '=', this.jobId], ['active', '=', true]],
            ['source_field', 'dest_field', 'mapping_type']
        );
        this.state.mappings = mappings;
        console.log('Loaded mappings:', mappings);

        // Trigger redraw after mappings are loaded
        setTimeout(() => {
            this.drawConnections();
        }, 100);
    }

    setupCanvas() {
        const canvas = this.canvasRef.el;
        if (!canvas) return;

        const container = canvas.parentElement;
        // Set canvas size
        canvas.width = container.offsetWidth;
        canvas.height = container.offsetHeight;

        // Set canvas style
        canvas.style.position = 'absolute';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.pointerEvents = 'none';
        canvas.style.zIndex = '10';

        // Add resize listener
        this.resizeHandler = () => {
            canvas.width = container.offsetWidth;
            canvas.height = container.offsetHeight;
            setTimeout(() => this.drawConnections(), 100);
        };
        window.addEventListener('resize', this.resizeHandler);

        // Initial draw after a short delay to ensure DOM is ready
        setTimeout(() => this.drawConnections(), 500);
    }

    onSourceFieldClick(fieldName) {
        if (this.state.isConnecting && this.state.selectedSourceField === fieldName) {
            this.cancelConnection();
        } else {
            this.startConnection(fieldName);
        }
    }

    onDestFieldClick(fieldName) {
        if (this.state.isConnecting && this.state.selectedSourceField) {
            this.createMapping(this.state.selectedSourceField, fieldName);
        }
    }

    startConnection(sourceField) {
        this.state.selectedSourceField = sourceField;
        this.state.isConnecting = true;
    }

    cancelConnection() {
        this.state.selectedSourceField = null;
        this.state.isConnecting = false;
    }

    async createMapping(sourceField, destField) {
        try {
            // Check if mapping already exists
            const existing = this.state.mappings.find(m => m.source_field === sourceField);
            
            if (existing) {
                // Update existing mapping
                await this.orm.write('migration.mapping', [existing.id], {
                    dest_field: destField
                });
                existing.dest_field = destField;
            } else {
                // Create new mapping
                const newMapping = await this.orm.create('migration.mapping', [{
                    job_id: this.jobId,
                    source_field: sourceField,
                    dest_field: destField,
                    mapping_type: 'direct',
                    active: true,
                }]);
                
                this.state.mappings.push({
                    id: newMapping[0],
                    source_field: sourceField,
                    dest_field: destField,
                    mapping_type: 'direct'
                });
            }
            
            this.notification.add(
                _t('Mapping created: %s → %s', sourceField, destField),
                { type: 'success' }
            );
            
            this.drawConnections();
            
        } catch (error) {
            console.error('Error creating mapping:', error);
            this.notification.add(_t('Error creating mapping'), { type: 'danger' });
        } finally {
            this.cancelConnection();
        }
    }

    async removeMapping(mapping) {
        try {
            await this.orm.unlink('migration.mapping', [mapping.id]);
            const index = this.state.mappings.findIndex(m => m.id === mapping.id);
            if (index > -1) {
                this.state.mappings.splice(index, 1);
            }
            this.drawConnections();
            this.notification.add(
                _t('Mapping removed: %s → %s', mapping.source_field, mapping.dest_field),
                { type: 'info' }
            );
        } catch (error) {
            console.error('Error removing mapping:', error);
            this.notification.add(_t('Error removing mapping'), { type: 'danger' });
        }
    }

    drawConnections() {
        const canvas = this.canvasRef.el;
        if (!canvas) {
            console.log('Canvas not found');
            return;
        }

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        console.log('Drawing connections for mappings:', this.state.mappings.length);

        this.state.mappings.forEach((mapping, index) => {
            console.log(`Drawing connection ${index + 1}:`, mapping.source_field, '->', mapping.dest_field);
            this.drawConnection(ctx, mapping);
        });
    }

    drawConnection(ctx, mapping) {
        const sourceEl = this.sourceListRef.el?.querySelector(`[data-field="${mapping.source_field}"]`);
        const destEl = this.destListRef.el?.querySelector(`[data-field="${mapping.dest_field}"]`);

        console.log('Looking for source element:', `[data-field="${mapping.source_field}"]`, sourceEl);
        console.log('Looking for dest element:', `[data-field="${mapping.dest_field}"]`, destEl);

        if (!sourceEl || !destEl) {
            console.log('Elements not found, skipping connection');
            return;
        }

        const canvasRect = this.canvasRef.el.getBoundingClientRect();
        const sourceRect = sourceEl.getBoundingClientRect();
        const destRect = destEl.getBoundingClientRect();

        const startX = sourceRect.right - canvasRect.left;
        const startY = sourceRect.top + sourceRect.height / 2 - canvasRect.top;
        const endX = destRect.left - canvasRect.left;
        const endY = destRect.top + destRect.height / 2 - canvasRect.top;

        console.log('Drawing line from', startX, startY, 'to', endX, endY);

        // Set line style based on mapping type
        ctx.strokeStyle = this.getMappingColor(mapping.mapping_type);
        ctx.lineWidth = 3;

        // Draw curved line
        ctx.beginPath();
        ctx.moveTo(startX, startY);

        const controlX1 = startX + (endX - startX) / 3;
        const controlX2 = startX + 2 * (endX - startX) / 3;

        ctx.bezierCurveTo(controlX1, startY, controlX2, endY, endX, endY);
        ctx.stroke();

        // Draw arrow
        this.drawArrow(ctx, controlX2, endY, endX, endY);
    }

    drawArrow(ctx, fromX, fromY, toX, toY) {
        const headlen = 8;
        const angle = Math.atan2(toY - fromY, toX - fromX);
        
        ctx.beginPath();
        ctx.moveTo(toX, toY);
        ctx.lineTo(
            toX - headlen * Math.cos(angle - Math.PI / 6),
            toY - headlen * Math.sin(angle - Math.PI / 6)
        );
        ctx.moveTo(toX, toY);
        ctx.lineTo(
            toX - headlen * Math.cos(angle + Math.PI / 6),
            toY - headlen * Math.sin(angle + Math.PI / 6)
        );
        ctx.stroke();
    }

    getMappingColor(mappingType) {
        const colors = {
            'direct': '#28a745',
            'transform': '#ffc107',
            'lookup': '#17a2b8',
            'skip': '#dc3545',
        };
        return colors[mappingType] || '#6c757d';
    }

    getFieldIcon(fieldType) {
        const icons = {
            'char': 'fa-font',
            'text': 'fa-align-left',
            'integer': 'fa-hashtag',
            'float': 'fa-calculator',
            'boolean': 'fa-check-square',
            'date': 'fa-calendar',
            'datetime': 'fa-clock-o',
            'many2one': 'fa-link',
            'one2many': 'fa-list',
            'many2many': 'fa-tags',
            'selection': 'fa-list-ul',
            'binary': 'fa-file',
        };
        return icons[fieldType] || 'fa-question';
    }

    async refreshData() {
        await this.loadData();
        // Force redraw after a delay to ensure DOM is updated
        setTimeout(() => {
            this.drawConnections();
        }, 200);
    }

    forceRedraw() {
        // Manual method to force canvas redraw
        setTimeout(() => {
            this.drawConnections();
        }, 100);
    }
}

// Register the widget
export const visualMappingWidget = {
    component: VisualMappingWidget,
};

registry.category("view_widgets").add("visual_mapping_widget", visualMappingWidget);
