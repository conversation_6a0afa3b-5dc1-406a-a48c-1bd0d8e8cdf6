<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Migration Tool Categories -->
        <record id="module_category_migration_tool" model="ir.module.category">
            <field name="name">Migration Tool</field>
            <field name="description">Manage access to migration tool features</field>
            <field name="sequence">50</field>
        </record>
        
        <!-- Migration Tool User Group -->
        <record id="group_migration_user" model="res.groups">
            <field name="name">Migration User</field>
            <field name="category_id" ref="module_category_migration_tool"/>
            <field name="comment">Can view and execute migration jobs</field>
        </record>
        
        <!-- Migration Tool Manager Group -->
        <record id="group_migration_manager" model="res.groups">
            <field name="name">Migration Manager</field>
            <field name="category_id" ref="module_category_migration_tool"/>
            <field name="implied_ids" eval="[(4, ref('group_migration_user'))]"/>
            <field name="comment">Can create, modify and manage migration configurations and jobs</field>
        </record>
        
        <!-- Migration Tool Administrator Group -->
        <record id="group_migration_admin" model="res.groups">
            <field name="name">Migration Administrator</field>
            <field name="category_id" ref="module_category_migration_tool"/>
            <field name="implied_ids" eval="[(4, ref('group_migration_manager'))]"/>
            <field name="comment">Full access to migration tool including system configurations</field>
        </record>
        
        <!-- Record Rules -->
        
        <!-- Migration Config Rules -->
        <record id="migration_config_rule_user" model="ir.rule">
            <field name="name">Migration Config: User Access</field>
            <field name="model_id" ref="model_migration_config"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_migration_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="migration_config_rule_manager" model="ir.rule">
            <field name="name">Migration Config: Manager Access</field>
            <field name="model_id" ref="model_migration_config"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_migration_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <!-- Migration Job Rules -->
        <record id="migration_job_rule_user" model="ir.rule">
            <field name="name">Migration Job: User Access</field>
            <field name="model_id" ref="model_migration_job"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_migration_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="migration_job_rule_manager" model="ir.rule">
            <field name="name">Migration Job: Manager Access</field>
            <field name="model_id" ref="model_migration_job"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_migration_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <!-- Migration Mapping Rules -->
        <record id="migration_mapping_rule_user" model="ir.rule">
            <field name="name">Migration Mapping: User Access</field>
            <field name="model_id" ref="model_migration_mapping"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_migration_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="migration_mapping_rule_manager" model="ir.rule">
            <field name="name">Migration Mapping: Manager Access</field>
            <field name="model_id" ref="model_migration_mapping"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_migration_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <!-- Migration Log Rules -->
        <record id="migration_log_rule_user" model="ir.rule">
            <field name="name">Migration Log: User Access</field>
            <field name="model_id" ref="model_migration_log"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_migration_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="migration_log_rule_manager" model="ir.rule">
            <field name="name">Migration Log: Manager Access</field>
            <field name="model_id" ref="model_migration_log"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_migration_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
    </data>
</odoo>
