# -*- coding: utf-8 -*-

from odoo import models, fields, api, tools, _


class MigrationLog(models.Model):
    _name = 'migration.log'
    _description = 'Migration Log'
    _rec_name = 'message'
    _order = 'create_date desc'

    # Relations
    job_id = fields.Many2one('migration.job', string='Migration Job', required=True, ondelete='cascade')
    config_id = fields.Many2one('migration.config', related='job_id.config_id', string='Configuration', store=True)
    
    # Log Information
    log_type = fields.Selection([
        ('info', 'Information'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('success', 'Success'),
        ('debug', 'Debug')
    ], string='Type', required=True, default='info')
    
    message = fields.Text(string='Message', required=True)
    details = fields.Text(string='Details')
    
    # Record Information
    source_record_id = fields.Integer(string='Source Record ID')
    dest_record_id = fields.Integer(string='Destination Record ID')
    record_data = fields.Text(string='Record Data')
    
    # Batch Information
    batch_number = fields.Integer(string='Batch Number')
    batch_size = fields.Integer(string='Batch Size')
    
    # Timing
    execution_time = fields.Float(string='Execution Time (seconds)')
    
    def name_get(self):
        """Custom name_get to show log type and message"""
        result = []
        for record in self:
            name = "[{}] {}".format(record.log_type.upper(), record.message[:50])
            if len(record.message) > 50:
                name += "..."
            result.append((record.id, name))
        return result
    
    @api.model
    def log_info(self, job_id, message, details=None, **kwargs):
        """Log an info message"""
        return self._create_log('info', job_id, message, details, **kwargs)
    
    @api.model
    def log_warning(self, job_id, message, details=None, **kwargs):
        """Log a warning message"""
        return self._create_log('warning', job_id, message, details, **kwargs)
    
    @api.model
    def log_error(self, job_id, message, details=None, **kwargs):
        """Log an error message"""
        return self._create_log('error', job_id, message, details, **kwargs)
    
    @api.model
    def log_success(self, job_id, message, details=None, **kwargs):
        """Log a success message"""
        return self._create_log('success', job_id, message, details, **kwargs)
    
    @api.model
    def log_debug(self, job_id, message, details=None, **kwargs):
        """Log a debug message"""
        return self._create_log('debug', job_id, message, details, **kwargs)
    
    @api.model
    def _create_log(self, log_type, job_id, message, details=None, **kwargs):
        """Create a log entry"""
        vals = {
            'job_id': job_id,
            'log_type': log_type,
            'message': message,
            'details': details,
        }
        vals.update(kwargs)
        return self.create(vals)
    
    def action_view_details(self):
        """View log details in a popup"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Log Details'),
            'res_model': 'migration.log',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }


class MigrationLogSummary(models.Model):
    _name = 'migration.log.summary'
    _description = 'Migration Log Summary'
    _auto = False
    _rec_name = 'job_name'

    job_id = fields.Many2one('migration.job', string='Migration Job')
    job_name = fields.Char(string='Job Name')
    config_name = fields.Char(string='Configuration')
    
    total_logs = fields.Integer(string='Total Logs')
    info_count = fields.Integer(string='Info')
    warning_count = fields.Integer(string='Warnings')
    error_count = fields.Integer(string='Errors')
    success_count = fields.Integer(string='Success')
    debug_count = fields.Integer(string='Debug')
    
    last_log_date = fields.Datetime(string='Last Log Date')
    
    def init(self):
        """Create the view"""
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
            CREATE OR REPLACE VIEW %s AS (
                SELECT
                    row_number() OVER () AS id,
                    ml.job_id,
                    mj.name AS job_name,
                    mc.name AS config_name,
                    COUNT(*) AS total_logs,
                    COUNT(CASE WHEN ml.log_type = 'info' THEN 1 END) AS info_count,
                    COUNT(CASE WHEN ml.log_type = 'warning' THEN 1 END) AS warning_count,
                    COUNT(CASE WHEN ml.log_type = 'error' THEN 1 END) AS error_count,
                    COUNT(CASE WHEN ml.log_type = 'success' THEN 1 END) AS success_count,
                    COUNT(CASE WHEN ml.log_type = 'debug' THEN 1 END) AS debug_count,
                    MAX(ml.create_date) AS last_log_date
                FROM migration_log ml
                JOIN migration_job mj ON ml.job_id = mj.id
                JOIN migration_config mc ON mj.config_id = mc.id
                GROUP BY ml.job_id, mj.name, mc.name
            )
        """ % self._table)
