# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class MigrationMapping(models.Model):
    _name = 'migration.mapping'
    _description = 'Migration Field Mapping'
    _rec_name = 'source_field'
    _order = 'sequence, source_field'

    sequence = fields.Integer(string='Sequence', default=10)
    active = fields.Boolean(string='Active', default=True)
    
    # Relations
    job_id = fields.Many2one('migration.job', string='Migration Job', required=True, ondelete='cascade')
    
    # Field Information
    source_field = fields.Char(string='Source Field', required=True)
    dest_field = fields.Char(string='Destination Field', required=True)
    field_type = fields.Char(string='Field Type', readonly=True)
    field_description = fields.Char(string='Description', readonly=True)
    relation_model = fields.Char(string='Relation Model', readonly=True)
    required = fields.Boolean(string='Required', readonly=True)
    readonly = fields.Boolean(string='Readonly', readonly=True)
    
    # Mapping Configuration
    mapping_type = fields.Selection([
        ('direct', 'Direct Mapping'),
        ('transform', 'Transform Value'),
        ('lookup', 'Lookup Table'),
        ('function', 'Custom Function'),
        ('skip', 'Skip Field')
    ], string='Mapping Type', default='direct', required=True)
    
    # Transform Configuration
    transform_expression = fields.Text(string='Transform Expression', 
                                     help='Python expression to transform the value. Use "value" as the source value.')
    
    # Lookup Configuration
    lookup_model = fields.Char(string='Lookup Model', help='Model to lookup values from')
    lookup_field = fields.Char(string='Lookup Field', help='Field to match in lookup model')
    lookup_return_field = fields.Char(string='Return Field', help='Field to return from lookup model')
    
    # Default Value
    default_value = fields.Char(string='Default Value', help='Default value if source field is empty')
    
    # Validation
    validate_required = fields.Boolean(string='Validate Required', default=True)
    validate_type = fields.Boolean(string='Validate Type', default=True)
    
    # Statistics
    success_count = fields.Integer(string='Success Count', readonly=True)
    error_count = fields.Integer(string='Error Count', readonly=True)
    last_error = fields.Text(string='Last Error', readonly=True)
    
    @api.onchange('mapping_type')
    def _onchange_mapping_type(self):
        """Clear configuration fields when mapping type changes"""
        if self.mapping_type != 'transform':
            self.transform_expression = False
        if self.mapping_type != 'lookup':
            self.lookup_model = False
            self.lookup_field = False
            self.lookup_return_field = False
    
    @api.constrains('transform_expression')
    def _check_transform_expression(self):
        """Validate transform expression syntax"""
        for record in self:
            if record.mapping_type == 'transform' and record.transform_expression:
                try:
                    # Test compile the expression
                    compile(record.transform_expression, '<string>', 'eval')
                except SyntaxError as e:
                    raise ValidationError(_('Invalid transform expression: %s') % str(e))
    
    def test_mapping(self, source_value):
        """Test the mapping with a sample value"""
        try:
            return self._apply_mapping(source_value)
        except Exception as e:
            return {'error': str(e)}
    
    def _apply_mapping(self, source_value):
        """Apply the mapping to a source value"""
        if self.mapping_type == 'skip':
            return None
        
        if self.mapping_type == 'direct':
            return source_value if source_value is not None else self.default_value
        
        elif self.mapping_type == 'transform':
            if not self.transform_expression:
                return source_value
            
            try:
                # Create safe evaluation context
                eval_context = {
                    'value': source_value,
                    'str': str,
                    'int': int,
                    'float': float,
                    'bool': bool,
                    'len': len,
                    'max': max,
                    'min': min,
                    'sum': sum,
                    'abs': abs,
                    'round': round,
                }
                
                result = eval(self.transform_expression, {"__builtins__": {}}, eval_context)
                return result if result is not None else self.default_value
                
            except Exception as e:
                raise ValidationError(_('Transform error for field %s: %s') % (self.source_field, str(e)))
        
        elif self.mapping_type == 'lookup':
            if not all([self.lookup_model, self.lookup_field, self.lookup_return_field]):
                raise ValidationError(_('Lookup configuration incomplete for field %s') % self.source_field)
            
            try:
                # Perform lookup in destination database
                dest_conn = self.job_id.config_id.get_dest_connection()
                cursor = dest_conn.cursor()
                
                table_name = self.lookup_model.replace('.', '_')
                query = "SELECT {} FROM {} WHERE {} = %s LIMIT 1".format(self.lookup_return_field, table_name, self.lookup_field)
                cursor.execute(query, (source_value,))
                
                result = cursor.fetchone()
                cursor.close()
                dest_conn.close()
                
                if result:
                    return result[0]
                else:
                    return self.default_value
                    
            except Exception as e:
                raise ValidationError(_('Lookup error for field %s: %s') % (self.source_field, str(e)))
        
        elif self.mapping_type == 'function':
            # For custom functions, this would need to be implemented based on specific requirements
            return source_value
        
        return source_value
    
    def action_test_mapping(self):
        """Open wizard to test the mapping"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Test Field Mapping'),
            'res_model': 'migration.mapping.test',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_mapping_id': self.id}
        }


class MigrationMappingTest(models.TransientModel):
    _name = 'migration.mapping.test'
    _description = 'Test Migration Mapping'

    mapping_id = fields.Many2one('migration.mapping', string='Mapping', required=True)
    test_value = fields.Char(string='Test Value')
    result_value = fields.Char(string='Result Value', readonly=True)
    error_message = fields.Text(string='Error Message', readonly=True)
    
    def action_test(self):
        """Test the mapping with the provided value"""
        try:
            result = self.mapping_id._apply_mapping(self.test_value)
            self.result_value = str(result) if result is not None else 'None'
            self.error_message = False
        except Exception as e:
            self.result_value = False
            self.error_message = str(e)
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'migration.mapping.test',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }
