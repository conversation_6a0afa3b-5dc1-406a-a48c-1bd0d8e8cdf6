# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

_logger = logging.getLogger(__name__)


class MigrationJob(models.Model):
    _name = 'migration.job'
    _description = 'Migration Job'
    _rec_name = 'name'
    _order = 'sequence, id'

    name = fields.Char(string='Job Name', required=True)
    sequence = fields.Integer(string='Sequence', default=10)
    active = fields.Boolean(string='Active', default=True)
    
    # Configuration
    config_id = fields.Many2one('migration.config', string='Migration Configuration', required=True, ondelete='cascade')
    
    # Migration Type
    migration_type = fields.Selection([
        ('model_data', 'Model Data'),
        ('views', 'Views'),
        ('security', 'Security Groups'),
        ('menus', 'Menus'),
        ('actions', 'Actions'),
        ('reports', 'Reports'),
        ('workflows', 'Workflows'),
        ('custom', 'Custom')
    ], string='Migration Type', required=True, default='model_data')
    
    # Model Configuration
    source_model = fields.Char(string='Source Model', help='Model name in source database')
    dest_model = fields.Char(string='Destination Model', help='Model name in destination database')
    
    # Migration Settings
    batch_size = fields.Integer(string='Batch Size', default=1000, help='Number of records to process in each batch')
    use_field_mapping = fields.Boolean(string='Use Field Mapping', default=True)
    skip_existing = fields.Boolean(string='Skip Existing Records', default=True)
    update_existing = fields.Boolean(string='Update Existing Records', default=False)
    
    # Filters
    domain_filter = fields.Text(string='Domain Filter', help='Domain filter to apply on source records (Python format)')
    date_from = fields.Datetime(string='Date From', help='Filter records from this date')
    date_to = fields.Datetime(string='Date To', help='Filter records to this date')
    
    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('ready', 'Ready'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', readonly=True)
    
    # Progress
    total_records = fields.Integer(string='Total Records', readonly=True)
    processed_records = fields.Integer(string='Processed Records', readonly=True)
    success_records = fields.Integer(string='Success Records', readonly=True)
    failed_records = fields.Integer(string='Failed Records', readonly=True)
    progress_percentage = fields.Float(string='Progress %', compute='_compute_progress', store=True)
    
    # Timing
    start_time = fields.Datetime(string='Start Time', readonly=True)
    end_time = fields.Datetime(string='End Time', readonly=True)
    duration = fields.Float(string='Duration (minutes)', compute='_compute_duration', store=True)
    
    # Relations
    mapping_ids = fields.One2many('migration.mapping', 'job_id', string='Field Mappings')
    log_ids = fields.One2many('migration.log', 'job_id', string='Migration Logs')
    
    # Error Handling
    error_message = fields.Text(string='Error Message', readonly=True)
    continue_on_error = fields.Boolean(string='Continue on Error', default=True)
    
    @api.depends('total_records', 'processed_records')
    def _compute_progress(self):
        for record in self:
            if record.total_records > 0:
                record.progress_percentage = (record.processed_records / record.total_records) * 100
            else:
                record.progress_percentage = 0.0
    
    @api.depends('start_time', 'end_time')
    def _compute_duration(self):
        for record in self:
            if record.start_time and record.end_time:
                delta = record.end_time - record.start_time
                record.duration = delta.total_seconds() / 60
            else:
                record.duration = 0.0
    
    def action_validate(self):
        """Validate the migration job configuration"""
        for record in self:
            if not record.source_model:
                raise ValidationError(_('Source model is required.'))
            
            if not record.dest_model:
                record.dest_model = record.source_model
            
            # Test database connections
            try:
                record.config_id.get_source_connection().close()
                record.config_id.get_dest_connection().close()
            except Exception as e:
                raise ValidationError(_('Database connection failed: %s') % str(e))
            
            record.state = 'ready'
    
    def action_reset_to_draft(self):
        """Reset job to draft state"""
        self.write({
            'state': 'draft',
            'total_records': 0,
            'processed_records': 0,
            'success_records': 0,
            'failed_records': 0,
            'start_time': False,
            'end_time': False,
            'error_message': False,
        })
    
    def action_cancel(self):
        """Cancel the migration job"""
        self.state = 'cancelled'
    
    def action_discover_fields(self):
        """Discover fields from source model and create mappings"""
        if not self.source_model:
            raise UserError(_('Please specify the source model first.'))
        
        try:
            # Get source database connection
            conn = self.config_id.get_source_connection()
            cursor = conn.cursor()
            
            # Get model fields from ir_model_fields
            cursor.execute("""
                SELECT name, field_description, ttype, relation, required, readonly
                FROM ir_model_fields 
                WHERE model = %s AND state = 'base'
                ORDER BY name
            """, (self.source_model,))
            
            fields_data = cursor.fetchall()
            cursor.close()
            conn.close()
            
            # Clear existing mappings
            self.mapping_ids.unlink()
            
            # Create field mappings
            mapping_vals = []
            for field_data in fields_data:
                name, description, ttype, relation, required, readonly = field_data
                mapping_vals.append({
                    'job_id': self.id,
                    'source_field': name,
                    'dest_field': name,  # Default to same name
                    'field_type': ttype,
                    'field_description': description,
                    'relation_model': relation,
                    'required': required,
                    'readonly': readonly,
                    'active': True,
                })
            
            self.env['migration.mapping'].create(mapping_vals)
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Discovered %d fields for model %s') % (len(fields_data), self.source_model),
                    'type': 'success',
                }
            }
            
        except Exception as e:
            raise UserError(_('Failed to discover fields: %s') % str(e))
    
    def action_count_records(self):
        """Count total records in source model"""
        if not self.source_model:
            raise UserError(_('Please specify the source model first.'))
        
        try:
            conn = self.config_id.get_source_connection()
            cursor = conn.cursor()
            
            # Build query with filters
            query = "SELECT COUNT(*) FROM {}".format(self.source_model.replace('.', '_'))
            params = []
            
            # Add date filters if specified
            where_conditions = []
            if self.date_from:
                where_conditions.append("create_date >= %s")
                params.append(self.date_from)
            if self.date_to:
                where_conditions.append("create_date <= %s")
                params.append(self.date_to)
            
            if where_conditions:
                query += " WHERE " + " AND ".join(where_conditions)
            
            cursor.execute(query, params)
            count = cursor.fetchone()[0]
            cursor.close()
            conn.close()
            
            self.total_records = count
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Record Count'),
                    'message': _('Found %d records in source model %s') % (count, self.source_model),
                    'type': 'info',
                }
            }
            
        except Exception as e:
            raise UserError(_('Failed to count records: %s') % str(e))
    
    def action_run_migration(self):
        """Run the migration job"""
        if self.state != 'ready':
            raise UserError(_('Job must be in Ready state to run migration.'))

        return {
            'type': 'ir.actions.act_window',
            'name': _('Run Migration'),
            'res_model': 'migration.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_job_id': self.id}
        }

    def action_visual_mapping(self):
        """Open visual field mapping interface"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Visual Field Mapping'),
            'res_model': 'migration.job',
            'res_id': self.id,
            'view_mode': 'form',
            'view_id': self.env.ref('odoo_migration_tool.migration_job_visual_mapping_view').id,
            'target': 'current',
        }

    def get_source_fields(self):
        """Get fields from source model"""
        if not self.source_model:
            return []

        try:
            conn = self.config_id.get_source_connection()
            cursor = conn.cursor()

            # Get model fields from ir_model_fields
            cursor.execute("""
                SELECT name, field_description, ttype, relation, required, readonly
                FROM ir_model_fields
                WHERE model = %s AND state = 'base'
                ORDER BY name
            """, (self.source_model,))

            fields_data = cursor.fetchall()
            cursor.close()
            conn.close()

            # Convert to list of dictionaries
            fields = []
            for field_data in fields_data:
                name, description, ttype, relation, required, readonly = field_data
                fields.append({
                    'name': name,
                    'description': description or name,
                    'type': ttype,
                    'relation': relation,
                    'required': required,
                    'readonly': readonly,
                })

            return fields

        except Exception as e:
            _logger.error("Failed to get source fields: %s", str(e))
            return []

    def get_dest_fields(self):
        """Get fields from destination model"""
        if not self.dest_model:
            return []

        try:
            # Use current database for destination fields
            model_obj = self.env['ir.model'].search([('model', '=', self.dest_model)], limit=1)
            if not model_obj:
                return []

            field_objs = self.env['ir.model.fields'].search([
                ('model_id', '=', model_obj.id),
                ('state', '=', 'base')
            ])

            fields = []
            for field_obj in field_objs:
                fields.append({
                    'name': field_obj.name,
                    'description': field_obj.field_description or field_obj.name,
                    'type': field_obj.ttype,
                    'relation': field_obj.relation,
                    'required': field_obj.required,
                    'readonly': field_obj.readonly,
                })

            return fields

        except Exception as e:
            _logger.error("Failed to get destination fields: %s", str(e))
            return []

    def auto_map_same_names(self):
        """Automatically map fields with the same names"""
        source_fields = self.get_source_fields()
        dest_fields = self.get_dest_fields()

        source_field_names = {f['name'] for f in source_fields}
        dest_field_names = {f['name'] for f in dest_fields}

        # Find common field names
        common_fields = source_field_names.intersection(dest_field_names)

        created_count = 0
        for field_name in common_fields:
            # Check if mapping already exists
            existing = self.mapping_ids.filtered(lambda m: m.source_field == field_name)
            if not existing:
                self.env['migration.mapping'].create({
                    'job_id': self.id,
                    'source_field': field_name,
                    'dest_field': field_name,
                    'mapping_type': 'direct',
                    'active': True,
                })
                created_count += 1

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Auto-mapping Complete'),
                'message': _('Created %d automatic mappings for fields with same names') % created_count,
                'type': 'success',
            }
        }

    def auto_map_similar_names(self):
        """Automatically map fields with similar names using fuzzy matching"""
        try:
            from difflib import SequenceMatcher
        except ImportError:
            raise UserError(_('difflib module not available for fuzzy matching'))

        source_fields = self.get_source_fields()
        dest_fields = self.get_dest_fields()

        created_count = 0
        similarity_threshold = 0.8  # 80% similarity

        for source_field in source_fields:
            # Skip if mapping already exists
            existing = self.mapping_ids.filtered(lambda m: m.source_field == source_field['name'])
            if existing:
                continue

            best_match = None
            best_similarity = 0

            for dest_field in dest_fields:
                # Calculate similarity
                similarity = SequenceMatcher(None, source_field['name'], dest_field['name']).ratio()

                if similarity > best_similarity and similarity >= similarity_threshold:
                    best_similarity = similarity
                    best_match = dest_field

            if best_match:
                self.env['migration.mapping'].create({
                    'job_id': self.id,
                    'source_field': source_field['name'],
                    'dest_field': best_match['name'],
                    'mapping_type': 'direct',
                    'active': True,
                })
                created_count += 1

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Auto-mapping Complete'),
                'message': _('Created %d automatic mappings for similar field names') % created_count,
                'type': 'success',
            }
        }

    def clear_all_mappings(self):
        """Clear all field mappings for this job"""
        mapping_count = len(self.mapping_ids)
        self.mapping_ids.unlink()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Mappings Cleared'),
                'message': _('Removed %d field mappings') % mapping_count,
                'type': 'info',
            }
        }
