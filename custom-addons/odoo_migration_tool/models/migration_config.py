# -*- coding: utf-8 -*-

import psycopg2
import logging
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

_logger = logging.getLogger(__name__)


class MigrationConfig(models.Model):
    _name = 'migration.config'
    _description = 'Migration Configuration'
    _rec_name = 'name'

    name = fields.Char(string='Configuration Name', required=True)
    active = fields.Boolean(string='Active', default=True)
    
    # Source Database Configuration (Odoo 17)
    source_host = fields.Char(string='Source Host', required=True, default='localhost')
    source_port = fields.Integer(string='Source Port', required=True, default=5432)
    source_database = fields.Char(string='Source Database', required=True)
    source_username = fields.Char(string='Source Username', required=True)
    source_password = fields.Char(string='Source Password', required=True)
    
    # Destination Database Configuration (Odoo 18)
    dest_host = fields.Char(string='Destination Host', required=True, default='localhost')
    dest_port = fields.Integer(string='Destination Port', required=True, default=5432)
    dest_database = fields.Char(string='Destination Database', required=True)
    dest_username = fields.Char(string='Destination Username', required=True)
    dest_password = fields.Char(string='Destination Password', required=True)
    
    # Connection Status
    source_connection_status = fields.Selection([
        ('not_tested', 'Not Tested'),
        ('connected', 'Connected'),
        ('failed', 'Connection Failed')
    ], string='Source Connection Status', default='not_tested', readonly=True)
    
    dest_connection_status = fields.Selection([
        ('not_tested', 'Not Tested'),
        ('connected', 'Connected'),
        ('failed', 'Connection Failed')
    ], string='Destination Connection Status', default='not_tested', readonly=True)
    
    source_connection_message = fields.Text(string='Source Connection Message', readonly=True)
    dest_connection_message = fields.Text(string='Destination Connection Message', readonly=True)
    
    # Migration Jobs
    migration_job_ids = fields.One2many('migration.job', 'config_id', string='Migration Jobs')
    
    def test_source_connection(self):
        """Test connection to source database"""
        try:
            conn = psycopg2.connect(
                host=self.source_host,
                port=self.source_port,
                database=self.source_database,
                user=self.source_username,
                password=self.source_password
            )
            conn.close()
            self.source_connection_status = 'connected'
            self.source_connection_message = 'Connection successful'
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Source database connection successful'),
                    'type': 'success',
                }
            }
        except Exception as e:
            self.source_connection_status = 'failed'
            self.source_connection_message = str(e)
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Source database connection failed: %s') % str(e),
                    'type': 'danger',
                }
            }
    
    def test_dest_connection(self):
        """Test connection to destination database"""
        try:
            conn = psycopg2.connect(
                host=self.dest_host,
                port=self.dest_port,
                database=self.dest_database,
                user=self.dest_username,
                password=self.dest_password
            )
            conn.close()
            self.dest_connection_status = 'connected'
            self.dest_connection_message = 'Connection successful'
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Destination database connection successful'),
                    'type': 'success',
                }
            }
        except Exception as e:
            self.dest_connection_status = 'failed'
            self.dest_connection_message = str(e)
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Destination database connection failed: %s') % str(e),
                    'type': 'danger',
                }
            }
    
    def test_both_connections(self):
        """Test both source and destination connections"""
        self.test_source_connection()
        self.test_dest_connection()
        
        if self.source_connection_status == 'connected' and self.dest_connection_status == 'connected':
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Both database connections successful'),
                    'type': 'success',
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Warning'),
                    'message': _('One or both database connections failed'),
                    'type': 'warning',
                }
            }
    
    def get_source_connection(self):
        """Get source database connection"""
        if self.source_connection_status != 'connected':
            raise UserError(_('Source database connection not established. Please test the connection first.'))
        
        try:
            return psycopg2.connect(
                host=self.source_host,
                port=self.source_port,
                database=self.source_database,
                user=self.source_username,
                password=self.source_password
            )
        except Exception as e:
            raise UserError(_('Failed to connect to source database: %s') % str(e))
    
    def get_dest_connection(self):
        """Get destination database connection"""
        if self.dest_connection_status != 'connected':
            raise UserError(_('Destination database connection not established. Please test the connection first.'))
        
        try:
            return psycopg2.connect(
                host=self.dest_host,
                port=self.dest_port,
                database=self.dest_database,
                user=self.dest_username,
                password=self.dest_password
            )
        except Exception as e:
            raise UserError(_('Failed to connect to destination database: %s') % str(e))
    
    def discover_source_models(self):
        """Discover models from source database"""
        try:
            conn = self.get_source_connection()
            cursor = conn.cursor()
            
            # Get all models from ir_model table
            cursor.execute("""
                SELECT model, name, info
                FROM ir_model 
                WHERE state = 'base'
                ORDER BY model
            """)
            
            models = cursor.fetchall()
            cursor.close()
            conn.close()
            
            return models
            
        except Exception as e:
            raise UserError(_('Failed to discover source models: %s') % str(e))
    
    @api.constrains('source_host', 'source_port', 'source_database')
    def _check_source_config(self):
        for record in self:
            if not all([record.source_host, record.source_port, record.source_database]):
                raise ValidationError(_('Source database configuration is incomplete.'))
    
    @api.constrains('dest_host', 'dest_port', 'dest_database')
    def _check_dest_config(self):
        for record in self:
            if not all([record.dest_host, record.dest_port, record.dest_database]):
                raise ValidationError(_('Destination database configuration is incomplete.'))
