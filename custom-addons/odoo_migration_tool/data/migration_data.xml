<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Default Migration Configuration -->
        <record id="default_migration_config" model="migration.config">
            <field name="name">Default Migration Configuration</field>
            <field name="source_host">localhost</field>
            <field name="source_port">5432</field>
            <field name="source_database">odoo17_db</field>
            <field name="source_username">odoo</field>
            <field name="source_password">odoo</field>
            <field name="dest_host">localhost</field>
            <field name="dest_port">5432</field>
            <field name="dest_database">odoo18_db</field>
            <field name="dest_username">odoo</field>
            <field name="dest_password">odoo</field>
            <field name="active">False</field>
        </record>
        
        <!-- Sample Migration Jobs for Common Models -->
        <record id="sample_job_res_partner" model="migration.job">
            <field name="name">Migrate Partners</field>
            <field name="config_id" ref="default_migration_config"/>
            <field name="migration_type">model_data</field>
            <field name="source_model">res.partner</field>
            <field name="dest_model">res.partner</field>
            <field name="batch_size">500</field>
            <field name="sequence">10</field>
            <field name="active">False</field>
        </record>
        
        <record id="sample_job_product_template" model="migration.job">
            <field name="name">Migrate Products</field>
            <field name="config_id" ref="default_migration_config"/>
            <field name="migration_type">model_data</field>
            <field name="source_model">product.template</field>
            <field name="dest_model">product.template</field>
            <field name="batch_size">200</field>
            <field name="sequence">20</field>
            <field name="active">False</field>
        </record>
        
        <record id="sample_job_account_move" model="migration.job">
            <field name="name">Migrate Invoices</field>
            <field name="config_id" ref="default_migration_config"/>
            <field name="migration_type">model_data</field>
            <field name="source_model">account.move</field>
            <field name="dest_model">account.move</field>
            <field name="batch_size">100</field>
            <field name="sequence">30</field>
            <field name="active">False</field>
        </record>
        
        <record id="sample_job_sale_order" model="migration.job">
            <field name="name">Migrate Sales Orders</field>
            <field name="config_id" ref="default_migration_config"/>
            <field name="migration_type">model_data</field>
            <field name="source_model">sale.order</field>
            <field name="dest_model">sale.order</field>
            <field name="batch_size">100</field>
            <field name="sequence">40</field>
            <field name="active">False</field>
        </record>
        
        <record id="sample_job_purchase_order" model="migration.job">
            <field name="name">Migrate Purchase Orders</field>
            <field name="config_id" ref="default_migration_config"/>
            <field name="migration_type">model_data</field>
            <field name="source_model">purchase.order</field>
            <field name="dest_model">purchase.order</field>
            <field name="batch_size">100</field>
            <field name="sequence">50</field>
            <field name="active">False</field>
        </record>
        
        <record id="sample_job_stock_picking" model="migration.job">
            <field name="name">Migrate Stock Pickings</field>
            <field name="config_id" ref="default_migration_config"/>
            <field name="migration_type">model_data</field>
            <field name="source_model">stock.picking</field>
            <field name="dest_model">stock.picking</field>
            <field name="batch_size">200</field>
            <field name="sequence">60</field>
            <field name="active">False</field>
        </record>
        
        <record id="sample_job_hr_employee" model="migration.job">
            <field name="name">Migrate Employees</field>
            <field name="config_id" ref="default_migration_config"/>
            <field name="migration_type">model_data</field>
            <field name="source_model">hr.employee</field>
            <field name="dest_model">hr.employee</field>
            <field name="batch_size">100</field>
            <field name="sequence">70</field>
            <field name="active">False</field>
        </record>
        
        <record id="sample_job_project_project" model="migration.job">
            <field name="name">Migrate Projects</field>
            <field name="config_id" ref="default_migration_config"/>
            <field name="migration_type">model_data</field>
            <field name="source_model">project.project</field>
            <field name="dest_model">project.project</field>
            <field name="batch_size">50</field>
            <field name="sequence">80</field>
            <field name="active">False</field>
        </record>
        
        <record id="sample_job_crm_lead" model="migration.job">
            <field name="name">Migrate CRM Leads</field>
            <field name="config_id" ref="default_migration_config"/>
            <field name="migration_type">model_data</field>
            <field name="source_model">crm.lead</field>
            <field name="dest_model">crm.lead</field>
            <field name="batch_size">200</field>
            <field name="sequence">90</field>
            <field name="active">False</field>
        </record>
        
        <record id="sample_job_ir_ui_view" model="migration.job">
            <field name="name">Migrate Views</field>
            <field name="config_id" ref="default_migration_config"/>
            <field name="migration_type">views</field>
            <field name="source_model">ir.ui.view</field>
            <field name="dest_model">ir.ui.view</field>
            <field name="batch_size">50</field>
            <field name="sequence">100</field>
            <field name="active">False</field>
        </record>
        
        <record id="sample_job_res_groups" model="migration.job">
            <field name="name">Migrate Security Groups</field>
            <field name="config_id" ref="default_migration_config"/>
            <field name="migration_type">security</field>
            <field name="source_model">res.groups</field>
            <field name="dest_model">res.groups</field>
            <field name="batch_size">50</field>
            <field name="sequence">110</field>
            <field name="active">False</field>
        </record>
        
    </data>
</odoo>
