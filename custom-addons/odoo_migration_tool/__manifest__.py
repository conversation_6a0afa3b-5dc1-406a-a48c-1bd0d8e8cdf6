# -*- coding: utf-8 -*-
{
    'name': 'Odoo Migration Tool',
    'version': '********.0',
    'category': 'Tools',
    'summary': 'Complete Odoo 17 to 18 Migration Tool',
    'description': """
Odoo Migration Tool
===================

A comprehensive tool to migrate data from Odoo 17 to Odoo 18.

Features:
---------
* Database connection configuration for source (v17) and destination (v18)
* Model discovery and field mapping interface
* Support for migrating models, views, security groups, and other data
* Batch processing for large datasets
* Real-time progress tracking
* Comprehensive error handling and logging
* Rollback support for failed migrations

This module provides a complete solution for migrating your Odoo 17 data to Odoo 18,
with an intuitive interface for mapping fields and managing the migration process.
    """,
    'author': 'Migration Tool Developer',
    'website': 'https://www.odoo.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'web',
    ],
    'external_dependencies': {
        'python': ['psycopg2'],
    },
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'data/migration_data.xml',
        'views/migration_config_views.xml',
        'views/migration_job_views.xml',
        'views/migration_mapping_views.xml',
        'views/migration_log_views.xml',
        'views/migration_visual_mapping_views.xml',
        'views/migration_menus.xml',
        'wizard/migration_wizard_views.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'odoo_migration_tool/static/src/css/field_mapping_widget.css',
            'odoo_migration_tool/static/src/js/field_mapping_widget.js',
            'odoo_migration_tool/static/src/js/migration_visual_mapping_form.js',
            'odoo_migration_tool/static/src/xml/field_mapping_widget.xml',
        ],
    },
    'demo': [],
    'installable': True,
    'auto_install': False,
    'application': True,
    'sequence': 1,
}
