# Odoo Migration Tool

A comprehensive tool for migrating data from Odoo 17 to Odoo 18.

## Overview

This module provides a complete solution for migrating your Odoo 17 data to Odoo 18. It includes database connection management, field mapping, batch processing, and comprehensive monitoring capabilities.

## Features

### 🔧 Database Configuration
- Configure source (Odoo 17) and destination (Odoo 18) database connections
- Test database connections before migration
- Support for different hosts, ports, and credentials

### 🔄 Migration Jobs
- Create migration jobs for different data types
- Support for models, views, security groups, menus, actions, reports, and workflows
- Configurable batch processing
- Flexible filtering options

### 🗺️ Field Mapping
- Automatic field discovery from source models
- Interactive field mapping interface
- Support for different mapping types:
  - Direct mapping
  - Value transformation with Python expressions
  - Lookup tables
  - Custom functions
  - Skip fields

### 📊 Progress Monitoring
- Real-time progress tracking
- Detailed migration logs
- Error handling and reporting
- Batch-level monitoring

### 🛡️ Safety Features
- Dry run mode for testing
- Skip existing records option
- Update existing records option
- Continue on error option
- Comprehensive error logging

## Installation

1. Copy the module to your Odoo 18 addons directory:
   ```
   cp -r odoo_migration_tool /path/to/odoo18/addons/
   ```

2. Install required Python dependencies:
   ```
   pip install psycopg2-binary
   ```

3. Update the app list in Odoo and install the module

4. Assign appropriate user groups:
   - Migration User: Can view and execute migrations
   - Migration Manager: Can create and manage migration jobs
   - Migration Administrator: Full access to all features

## Usage

### 1. Configure Database Connections

1. Go to **Migration Tool > Configuration > Database Configurations**
2. Create a new configuration
3. Fill in source database details (Odoo 17)
4. Fill in destination database details (Odoo 18)
5. Test both connections

### 2. Create Migration Jobs

1. Go to **Migration Tool > Migration Jobs > Jobs**
2. Create a new migration job
3. Select the configuration
4. Choose migration type and source/destination models
5. Configure batch size and other settings
6. Validate the job

### 3. Set Up Field Mappings

1. Open the migration job
2. Click "Discover Fields" to automatically create field mappings
3. Review and modify mappings as needed
4. Configure transformations, lookups, or skip fields as required
5. Test individual mappings if needed

### 4. Run Migration

1. Ensure the job is in "Ready" state
2. Click "Run Migration"
3. Configure execution options:
   - Dry Run: Test without inserting data
   - Force Update: Update existing records
   - Ignore Errors: Continue on errors
4. Start the migration and monitor progress

### 5. Monitor Progress

1. View real-time progress in the migration wizard
2. Check detailed logs in **Migration Tool > Monitoring > Migration Logs**
3. Review log summary for overview

## Field Mapping Types

### Direct Mapping
Maps source field directly to destination field.

### Transform
Uses Python expressions to transform values:
```python
# Convert to uppercase
value.upper() if value else ''

# Add prefix
'PREFIX_' + str(value) if value else ''

# Date formatting
value.strftime('%Y-%m-%d') if value else ''
```

### Lookup
Looks up values in destination database:
- Lookup Model: Model to search in
- Lookup Field: Field to match
- Return Field: Field to return

### Skip
Skips the field during migration.

## Security

The module includes three security groups:

- **Migration User**: Basic access to view and execute migrations
- **Migration Manager**: Can create and manage migration configurations and jobs
- **Migration Administrator**: Full access including system configurations

## Troubleshooting

### Connection Issues
- Verify database credentials
- Check network connectivity
- Ensure PostgreSQL is accessible
- Verify database names exist

### Migration Errors
- Check migration logs for detailed error messages
- Verify field mappings are correct
- Test with dry run first
- Use smaller batch sizes for large datasets

### Performance Issues
- Reduce batch size
- Add database indexes on frequently queried fields
- Run migrations during off-peak hours
- Monitor system resources

## Technical Details

### Dependencies
- psycopg2: PostgreSQL adapter for Python
- Odoo 18.0+

### Database Requirements
- PostgreSQL 10+
- Network access between source and destination databases
- Sufficient disk space for migration logs

### Supported Migration Types
- Model Data: Regular Odoo models
- Views: UI views and templates
- Security: Groups and access rights
- Menus: Menu items and structure
- Actions: Window actions and reports
- Reports: Report templates
- Workflows: Business process workflows

## Contributing

Contributions are welcome! Please follow these guidelines:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This module is licensed under LGPL-3.

## Support

For support, please:
1. Check the documentation
2. Review migration logs for error details
3. Test with smaller datasets first
4. Contact the development team

## Changelog

### Version 18.0.1.0.0
- Initial release
- Database connection management
- Migration job creation and execution
- Field mapping with transformations
- Progress monitoring and logging
- Security groups and access control
