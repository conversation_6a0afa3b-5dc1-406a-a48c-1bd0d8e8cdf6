<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Migration Wizard Form View -->
        <record id="migration_wizard_view_form" model="ir.ui.view">
            <field name="name">migration.wizard.form</field>
            <field name="model">migration.wizard</field>
            <field name="arch" type="xml">
                <form string="Run Migration">
                    <header>
                        <button name="action_start_migration" string="Start Migration" type="object" 
                                class="btn-primary" invisible="state != 'draft'"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,running,completed"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <span>Migration: </span>
                                <field name="job_name" readonly="1"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group string="Job Information">
                                <field name="job_id" readonly="1"/>
                                <field name="source_model" readonly="1"/>
                                <field name="dest_model" readonly="1"/>
                            </group>
                            <group string="Execution Options">
                                <field name="dry_run" invisible="state != 'draft'"/>
                                <field name="force_update" invisible="state != 'draft'"/>
                                <field name="ignore_errors" invisible="state != 'draft'"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Progress" name="progress">
                                <group>
                                    <group string="Batch Progress">
                                        <field name="current_batch" readonly="1"/>
                                        <field name="total_batches" readonly="1"/>
                                    </group>
                                    <group string="Record Progress">
                                        <field name="processed_records" readonly="1"/>
                                        <field name="success_records" readonly="1"/>
                                        <field name="failed_records" readonly="1"/>
                                    </group>
                                </group>
                                
                                <group string="Status Message" invisible="not progress_message">
                                    <field name="progress_message" readonly="1" nolabel="1"/>
                                </group>
                            </page>
                            
                            <page string="Options Help" name="help">
                                <div class="alert alert-info">
                                    <h4>Execution Options:</h4>
                                    <ul>
                                        <li><strong>Dry Run:</strong> Test the migration without actually inserting data. Use this to validate your field mappings and identify potential issues.</li>
                                        <li><strong>Force Update:</strong> Update existing records even if "Skip Existing" is enabled in the job configuration.</li>
                                        <li><strong>Ignore Errors:</strong> Continue the migration process even if some records fail to migrate. Failed records will be logged for review.</li>
                                    </ul>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <h4>Important Notes:</h4>
                                    <ul>
                                        <li>Always test your migration with a small dataset first.</li>
                                        <li>Ensure both source and destination databases are accessible.</li>
                                        <li>Review field mappings before running the migration.</li>
                                        <li>Monitor the migration logs for any issues.</li>
                                        <li>Consider running a dry run first to validate the process.</li>
                                    </ul>
                                </div>
                            </page>
                        </notebook>
                    </sheet>
                    <footer>
                        <button string="Close" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>
        
    </data>
</odoo>
