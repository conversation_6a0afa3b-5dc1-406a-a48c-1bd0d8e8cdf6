# -*- coding: utf-8 -*-

import json
import time
import logging
from datetime import datetime
from odoo import models, fields, api, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class MigrationWizard(models.TransientModel):
    _name = 'migration.wizard'
    _description = 'Migration Execution Wizard'

    job_id = fields.Many2one('migration.job', string='Migration Job', required=True)
    job_name = fields.Char(related='job_id.name', string='Job Name', readonly=True)
    source_model = fields.Char(related='job_id.source_model', string='Source Model', readonly=True)
    dest_model = fields.Char(related='job_id.dest_model', string='Destination Model', readonly=True)
    
    # Execution Options
    dry_run = fields.Boolean(string='Dry Run', default=False, 
                           help='Test the migration without actually inserting data')
    force_update = fields.Boolean(string='Force Update', default=False,
                                help='Update existing records even if skip_existing is enabled')
    ignore_errors = fields.Boolean(string='Ignore Errors', default=True,
                                 help='Continue migration even if some records fail')
    
    # Progress
    state = fields.Selection([
        ('draft', 'Ready to Start'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ], string='State', default='draft')
    
    progress_message = fields.Text(string='Progress Message', readonly=True)
    current_batch = fields.Integer(string='Current Batch', readonly=True)
    total_batches = fields.Integer(string='Total Batches', readonly=True)
    processed_records = fields.Integer(string='Processed Records', readonly=True)
    success_records = fields.Integer(string='Success Records', readonly=True)
    failed_records = fields.Integer(string='Failed Records', readonly=True)
    
    def action_start_migration(self):
        """Start the migration process"""
        if self.state != 'draft':
            raise UserError(_('Migration can only be started from draft state.'))
        
        try:
            self.state = 'running'
            self.job_id.write({
                'state': 'running',
                'start_time': datetime.now(),
                'processed_records': 0,
                'success_records': 0,
                'failed_records': 0,
                'error_message': False,
            })
            
            # Log start of migration
            self.env['migration.log'].log_info(
                self.job_id.id,
                'Migration started for model {}'.format(self.source_model),
                'Dry run: {}, Force update: {}'.format(self.dry_run, self.force_update)
            )
            
            # Execute migration
            self._execute_migration()
            
            # Update final status
            if self.failed_records == 0:
                self.state = 'completed'
                self.job_id.state = 'completed'
                final_message = 'Migration completed successfully. Processed {} records.'.format(self.success_records)
            else:
                if self.success_records > 0:
                    self.state = 'completed'
                    self.job_id.state = 'completed'
                    final_message = 'Migration completed with errors. Success: {}, Failed: {}'.format(self.success_records, self.failed_records)
                else:
                    self.state = 'failed'
                    self.job_id.state = 'failed'
                    final_message = 'Migration failed. All {} records failed.'.format(self.failed_records)
            
            self.job_id.write({
                'end_time': datetime.now(),
                'processed_records': self.processed_records,
                'success_records': self.success_records,
                'failed_records': self.failed_records,
            })
            
            self.progress_message = final_message
            
            # Log completion
            log_type = 'success' if self.failed_records == 0 else 'warning' if self.success_records > 0 else 'error'
            self.env['migration.log'].create({
                'job_id': self.job_id.id,
                'log_type': log_type,
                'message': final_message,
            })
            
        except Exception as e:
            self.state = 'failed'
            self.job_id.write({
                'state': 'failed',
                'end_time': datetime.now(),
                'error_message': str(e),
            })
            
            # Log error
            self.env['migration.log'].log_error(
                self.job_id.id,
                'Migration failed: {}'.format(str(e)),
                details=str(e)
            )
            
            raise UserError(_('Migration failed: %s') % str(e))
        
        return self._return_wizard_action()
    
    def _execute_migration(self):
        """Execute the actual migration"""
        # Get database connections
        source_conn = self.job_id.config_id.get_source_connection()
        dest_conn = self.job_id.config_id.get_dest_connection()
        
        try:
            # Get source records
            source_records = self._get_source_records(source_conn)
            total_records = len(source_records)
            
            if total_records == 0:
                self.progress_message = 'No records found to migrate.'
                return
            
            # Calculate batches
            batch_size = self.job_id.batch_size or 1000
            self.total_batches = (total_records + batch_size - 1) // batch_size
            
            self.progress_message = 'Starting migration of {} records in {} batches.'.format(total_records, self.total_batches)
            
            # Process in batches
            for batch_num in range(self.total_batches):
                start_idx = batch_num * batch_size
                end_idx = min(start_idx + batch_size, total_records)
                batch_records = source_records[start_idx:end_idx]
                
                self.current_batch = batch_num + 1
                self.progress_message = 'Processing batch {} of {} ({} records)'.format(self.current_batch, self.total_batches, len(batch_records))
                
                # Process batch
                batch_success, batch_failed = self._process_batch(batch_records, dest_conn, batch_num + 1)
                
                self.success_records += batch_success
                self.failed_records += batch_failed
                self.processed_records = self.success_records + self.failed_records
                
                # Commit after each batch
                self.env.cr.commit()
                
                # Log batch completion
                self.env['migration.log'].log_info(
                    self.job_id.id,
                    'Batch {} completed'.format(self.current_batch),
                    'Success: {}, Failed: {}'.format(batch_success, batch_failed),
                    batch_number=self.current_batch,
                    batch_size=len(batch_records)
                )
                
        finally:
            source_conn.close()
            dest_conn.close()
    
    def _get_source_records(self, source_conn):
        """Get records from source database"""
        cursor = source_conn.cursor()
        
        try:
            # Build query
            table_name = self.source_model.replace('.', '_')
            query = "SELECT * FROM {}".format(table_name)
            params = []
            
            # Add filters
            where_conditions = []
            if self.job_id.date_from:
                where_conditions.append("create_date >= %s")
                params.append(self.job_id.date_from)
            if self.job_id.date_to:
                where_conditions.append("create_date <= %s")
                params.append(self.job_id.date_to)
            
            if where_conditions:
                query += " WHERE " + " AND ".join(where_conditions)
            
            query += " ORDER BY id"
            
            cursor.execute(query, params)
            
            # Get column names
            columns = [desc[0] for desc in cursor.description]
            
            # Fetch all records
            records = []
            for row in cursor.fetchall():
                record_dict = dict(zip(columns, row))
                records.append(record_dict)
            
            return records
            
        finally:
            cursor.close()
    
    def _process_batch(self, batch_records, dest_conn, batch_number):
        """Process a batch of records"""
        success_count = 0
        failed_count = 0
        
        for record in batch_records:
            try:
                if self._migrate_record(record, dest_conn):
                    success_count += 1
                else:
                    failed_count += 1
                    
            except Exception as e:
                failed_count += 1
                
                # Log individual record error
                self.env['migration.log'].log_error(
                    self.job_id.id,
                    'Failed to migrate record ID {}'.format(record.get("id", "unknown")),
                    details=str(e),
                    source_record_id=record.get('id'),
                    batch_number=batch_number
                )
                
                if not self.ignore_errors:
                    raise
        
        return success_count, failed_count
    
    def _migrate_record(self, source_record, dest_conn):
        """Migrate a single record"""
        if self.dry_run:
            # In dry run mode, just validate the mapping
            try:
                self._apply_field_mappings(source_record)
                return True
            except Exception:
                return False
        
        try:
            # Apply field mappings
            dest_record = self._apply_field_mappings(source_record)
            
            # Insert/update in destination
            return self._insert_or_update_record(dest_record, dest_conn)
            
        except Exception as e:
            _logger.error("Failed to migrate record %s: %s", source_record.get('id'), str(e))
            return False
    
    def _apply_field_mappings(self, source_record):
        """Apply field mappings to transform source record to destination format"""
        dest_record = {}
        
        for mapping in self.job_id.mapping_ids.filtered('active'):
            if mapping.mapping_type == 'skip':
                continue
                
            source_value = source_record.get(mapping.source_field)
            
            try:
                dest_value = mapping._apply_mapping(source_value)
                if dest_value is not None:
                    dest_record[mapping.dest_field] = dest_value
                    
            except Exception as e:
                # Log mapping error
                self.env['migration.log'].log_error(
                    self.job_id.id,
                    'Field mapping error for {}'.format(mapping.source_field),
                    details=str(e),
                    source_record_id=source_record.get('id')
                )
                
                # Use default value or skip
                if mapping.default_value:
                    dest_record[mapping.dest_field] = mapping.default_value
        
        return dest_record
    
    def _insert_or_update_record(self, dest_record, dest_conn):
        """Insert or update record in destination database"""
        cursor = dest_conn.cursor()
        
        try:
            table_name = self.dest_model.replace('.', '_')
            
            # Check if record exists (if skip_existing or update_existing is enabled)
            if self.job_id.skip_existing or self.job_id.update_existing:
                record_id = dest_record.get('id')
                if record_id:
                    cursor.execute("SELECT id FROM {} WHERE id = %s".format(table_name), (record_id,))
                    exists = cursor.fetchone()
                    
                    if exists:
                        if self.job_id.skip_existing and not self.force_update:
                            return True  # Skip existing record
                        elif self.job_id.update_existing or self.force_update:
                            # Update existing record
                            return self._update_record(dest_record, cursor, table_name)
            
            # Insert new record
            return self._insert_record(dest_record, cursor, table_name)
            
        finally:
            cursor.close()
    
    def _insert_record(self, record, cursor, table_name):
        """Insert a new record"""
        if not record:
            return False
            
        columns = list(record.keys())
        values = list(record.values())
        placeholders = ', '.join(['%s'] * len(values))
        
        query = "INSERT INTO {} ({}) VALUES ({})".format(table_name, ', '.join(columns), placeholders)
        cursor.execute(query, values)
        
        return True
    
    def _update_record(self, record, cursor, table_name):
        """Update an existing record"""
        if not record or 'id' not in record:
            return False
            
        record_id = record.pop('id')
        if not record:  # No fields to update
            return True
            
        set_clause = ', '.join(["{} = %s".format(col) for col in record.keys()])
        values = list(record.values()) + [record_id]

        query = "UPDATE {} SET {} WHERE id = %s".format(table_name, set_clause)
        cursor.execute(query, values)
        
        return True
    
    def _return_wizard_action(self):
        """Return action to keep wizard open"""
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'migration.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }
