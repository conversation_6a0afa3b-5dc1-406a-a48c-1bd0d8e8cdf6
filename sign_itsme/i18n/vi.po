# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sign_itsme
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sign_itsme
#: model_terms:ir.ui.view,arch_db:sign_itsme.sign_request_logs_user
msgid ""
"<small>Name: The signatory has provided this identity through itsme®</small>"
msgstr ""
"<small>Tên: Người ký đã cung cấp thông tin danh tính này qua itsme®</small>"

#. module: sign_itsme
#: model:iap.service,description:sign_itsme.iap_service_itsme_proxy
msgid ""
"Ask document signatories in Odoo Sign to provide their identity using the "
"itsme® identity platform. By combining Odoo Sign with itsme®️, you can add "
"an identification step in your signature flows and ask signatories to "
"provide their identity through the itsme®️ platform, using their mobile "
"device. Available in Belgium and in the Netherlands."
msgstr ""
"Yêu cầu người ký tài liệu trong Odoo Ký tên cung cấp thông tin định danh của"
" họ qua nền tảng định danh itsme®. Bằng cách tích hợp Odoo Ký tên với "
"itsme®️, bạn có thể thêm bước định danh vào quy trình chữ ký của mình và yêu"
" cầu người ký cung cấp thông tin định danh của họ thông qua nền tảng itsme®️"
" bằng thiết bị di động của họ. Khả dụng tại Bỉ và Hà Lan."

#. module: sign_itsme
#: model:iap.service,unit_name:sign_itsme.iap_service_itsme_proxy
msgid "Credits"
msgstr "Tín dụng"

#. module: sign_itsme
#: model:sign.item.role,name:sign_itsme.sign_item_role_itsme_customer
msgid "Customer (identified with itsme®)"
msgstr "Khách hàng (được định danh bằng itsme®)"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/components/document_signable.js:0
msgid "Error"
msgstr "Lỗi"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_item_role__auth_method
msgid "Extra Authentication Step"
msgstr "Bước xác thực bổ sung"

#. module: sign_itsme
#: model:ir.model.fields,help:sign_itsme.field_sign_item_role__auth_method
msgid "Force the signatory to identify using a second authentication method"
msgstr "Buộc người ký tên xác nhận bằng phương thức xác thực thứ hai"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/dialogs/itsme_dialog.xml:0
msgid "Go Back"
msgstr "Quay lại"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/components/document_signable.js:0
msgid "Identification refused"
msgstr "Định danh bị từ chối"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/dialogs/itsme_dialog.xml:0
msgid "Identify with itsme"
msgstr "Định danh bằng itsme"

#. module: sign_itsme
#: model_terms:ir.ui.view,arch_db:sign_itsme.sign_request_logs_user
msgid "Name"
msgstr "Tên"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/dialogs/itsme_dialog.xml:0
msgid "Please confirm your identity to finalize your signature."
msgstr "Vui lòng xác nhận danh tính để hoàn tất chữ ký của bạn."

#. module: sign_itsme
#. odoo-python
#: code:addons/sign_itsme/models/sign_request_item.py:0
msgid "Sign request item is not validated yet."
msgstr "Mục yêu cầu ký chưa được xác thực."

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_item_role
msgid "Signature Item Party"
msgstr "Mục chữ ký Bên"

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_request
msgid "Signature Request"
msgstr "Yêu cầu ký tên"

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_request_item
msgid "Signature Request Item"
msgstr "Mục yêu cầu ký tên"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/components/document_signable.js:0
msgid ""
"The itsme® identification data could not be forwarded to Odoo, the signature"
" could not be saved."
msgstr ""
"Không thể chuyển tiếp dữ liệu định danh itsme® tới Odoo, nên không thể lưu "
"chữ ký."

#. module: sign_itsme
#: model:ir.model.fields.selection,name:sign_itsme.selection__sign_item_role__auth_method__itsme
msgid "Via itsme®"
msgstr "Qua itsme®"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/components/document_signable.js:0
msgid ""
"You have rejected the identification request or took too long to process it."
" You can try again to finalize your signature."
msgstr ""
"Bạn đã từ chối yêu cầu định danh hoặc mất quá nhiều thời gian để xử lý yêu "
"cầu đó. Bạn có thể thử lại để hoàn tất chữ ký của mình."

#. module: sign_itsme
#. odoo-python
#: code:addons/sign_itsme/controllers/main.py:0
msgid "itsme® IAP service could not be found."
msgstr "Không thể tìm thấy dịch vụ IAP itme®."

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_signer_birthdate
msgid "itsme® Signer's Birthdate"
msgstr "itsme® Ngày sinh của người ký"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_signer_name
msgid "itsme® Signer's Name"
msgstr "itsme® Tên của người ký"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_validation_hash
msgid "itsme® Validation Token"
msgstr "Token xác thực itsme®"
