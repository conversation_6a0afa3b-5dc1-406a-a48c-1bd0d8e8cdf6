# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap_extract
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:53+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: iap_extract
#: model:mail.template,body_html:iap_extract.iap_extract_no_credit
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p>Dear,<br/></p>\n"
"    <p>There are no more credits on your IAP OCR account.<br/>\n"
"    You can charge your IAP OCR account in the settings page.</p>\n"
"    <p>Best regards,<br/></p>\n"
"    <p>Odoo S.A.</p>\n"
"</div>"
msgstr ""

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__message_needaction
msgid "Action Needed"
msgstr "Potreben je ukrep"

#. module: iap_extract
#. odoo-javascript
#: code:addons/iap_extract/static/src/components/status_header/status.xml:0
msgid ""
"All fields will be automatically populated by Artificial Intelligence, it "
"might take 5 seconds."
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
#: model:ir.model.fields.selection,name:iap_extract.selection__account_bank_statement__extract_state__error_status
#: model:ir.model.fields.selection,name:iap_extract.selection__account_move__extract_state__error_status
#: model:ir.model.fields.selection,name:iap_extract.selection__extract_mixin__extract_state__error_status
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_candidate__extract_state__error_status
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_expense__extract_state__error_status
msgid "An error occurred"
msgstr "Pojavila se je napaka"

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "An error occurred during the upload"
msgstr ""

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__message_attachment_count
msgid "Attachment Count"
msgstr "Število prilog"

#. module: iap_extract
#: model:ir.model,name:iap_extract.model_extract_mixin
msgid "Base class to extract data from documents"
msgstr ""

#. module: iap_extract
#. odoo-javascript
#: code:addons/iap_extract/static/src/components/status_header/status.xml:0
msgid "Buy credits"
msgstr "Kupite kredite"

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "Lahko prikaže gumb za pošiljanje OCR"

#. module: iap_extract
#: model:ir.model.fields.selection,name:iap_extract.selection__account_bank_statement__extract_state__done
#: model:ir.model.fields.selection,name:iap_extract.selection__account_move__extract_state__done
#: model:ir.model.fields.selection,name:iap_extract.selection__extract_mixin__extract_state__done
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_candidate__extract_state__done
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_expense__extract_state__done
msgid "Completed flow"
msgstr ""

#. module: iap_extract
#: model:iap.service,description:iap_extract.iap_service_ocr
msgid ""
"Digitize your scanned or PDF vendor bills, expenses and resumes with OCR and"
" Artificial Intelligence."
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "Document is being digitized"
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "Document sent for digitization"
msgstr ""

#. module: iap_extract
#. odoo-javascript
#: code:addons/iap_extract/static/src/components/status_header/status.xml:0
msgid "Document successfully parsed. Please refresh."
msgstr ""

#. module: iap_extract
#: model:iap.service,unit_name:iap_extract.iap_service_ocr
msgid "Documents"
msgstr "Dokumenti"

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "Documents sent for digitization"
msgstr ""

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__extract_error_message
msgid "Error message"
msgstr "Sporočilo o napaki"

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__extract_state_processed
msgid "Extract State Processed"
msgstr "Izvleček Stanje Obdelano"

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__extract_state
msgid "Extract state"
msgstr "Stanje izpisa"

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__extract_status
msgid "Extract status"
msgstr "Stanje izvlečka"

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__message_follower_ids
msgid "Followers"
msgstr "Sledilci"

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sledilci (partnerji)"

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__has_message
msgid "Has Message"
msgstr "Ima sporočilo"

#. module: iap_extract
#: model:mail.template,name:iap_extract.iap_extract_no_credit
#: model:mail.template,subject:iap_extract.iap_extract_no_credit
msgid "IAP Extract Notification"
msgstr ""

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr "ID zahteve za IAP-OCR"

#. module: iap_extract
#: model:ir.model.fields,help:iap_extract.field_extract_mixin__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Če je označeno, zahtevajo nova sporočila vašo pozornost."

#. module: iap_extract
#: model:ir.model.fields,help:iap_extract.field_extract_mixin__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Če je označeno, nekatera sporočila vsebujejo napako pri dostavi."

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "Invalid PDF (Conversion error)"
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "Invalid PDF (Unable to get page count)"
msgstr ""

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__message_is_follower
msgid "Is Follower"
msgstr "Je sledilec"

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__is_in_extractable_state
msgid "Is In Extractable State"
msgstr "Je v stanju, ki ga je mogoče izvleči"

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna priponka"

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__message_has_error
msgid "Message Delivery error"
msgstr "Napaka pri dostavi sporočila"

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__message_ids
msgid "Messages"
msgstr "Sporočila"

#. module: iap_extract
#: model:ir.model.fields.selection,name:iap_extract.selection__account_bank_statement__extract_state__no_extract_requested
#: model:ir.model.fields.selection,name:iap_extract.selection__account_move__extract_state__no_extract_requested
#: model:ir.model.fields.selection,name:iap_extract.selection__extract_mixin__extract_state__no_extract_requested
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_candidate__extract_state__no_extract_requested
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_expense__extract_state__no_extract_requested
msgid "No extract requested"
msgstr ""

#. module: iap_extract
#: model:ir.model.fields.selection,name:iap_extract.selection__account_bank_statement__extract_state__not_enough_credit
#: model:ir.model.fields.selection,name:iap_extract.selection__account_move__extract_state__not_enough_credit
#: model:ir.model.fields.selection,name:iap_extract.selection__extract_mixin__extract_state__not_enough_credit
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_candidate__extract_state__not_enough_credit
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_expense__extract_state__not_enough_credit
msgid "Not enough credits"
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "Not enough credits for data extraction"
msgstr ""

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__message_needaction_counter
msgid "Number of Actions"
msgstr "Število aktivnosti"

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__message_has_error_counter
msgid "Number of errors"
msgstr "Število napak"

#. module: iap_extract
#: model:ir.model.fields,help:iap_extract.field_extract_mixin__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Število sporočil, ki zahtevajo ukrepanje"

#. module: iap_extract
#: model:ir.model.fields,help:iap_extract.field_extract_mixin__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Število sporočil, ki niso bila pravilno dostavljena."

#. module: iap_extract
#: model:ir.model.fields,field_description:iap_extract.field_extract_mixin__rating_ids
msgid "Ratings"
msgstr "Ocene"

#. module: iap_extract
#. odoo-javascript
#: code:addons/iap_extract/static/src/components/status_header/status.xml:0
msgid "Refresh"
msgstr "Osvežite"

#. module: iap_extract
#. odoo-javascript
#: code:addons/iap_extract/static/src/components/status_header/status.xml:0
msgid "Retry"
msgstr "Ponovno"

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "Server is currently under maintenance. Please retry later"
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "Server not available. Please retry later"
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "Some documents were skipped as they were already digitized"
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid ""
"The 'invoice_ocr' IAP account token is invalid. Please delete it to let Odoo"
" generate a new one or fill it with a valid token."
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "The document could not be found"
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "The document has been rejected because it is too small"
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "The selected documents are already digitized"
msgstr ""

#. module: iap_extract
#: model:ir.model.fields.selection,name:iap_extract.selection__account_bank_statement__extract_state__to_validate
#: model:ir.model.fields.selection,name:iap_extract.selection__account_move__extract_state__to_validate
#: model:ir.model.fields.selection,name:iap_extract.selection__extract_mixin__extract_state__to_validate
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_candidate__extract_state__to_validate
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_expense__extract_state__to_validate
msgid "To validate"
msgstr "Za potrditev"

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "Unsupported image format"
msgstr ""

#. module: iap_extract
#: model:ir.model.fields.selection,name:iap_extract.selection__account_bank_statement__extract_state__waiting_extraction
#: model:ir.model.fields.selection,name:iap_extract.selection__account_move__extract_state__waiting_extraction
#: model:ir.model.fields.selection,name:iap_extract.selection__extract_mixin__extract_state__waiting_extraction
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_candidate__extract_state__waiting_extraction
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_expense__extract_state__waiting_extraction
msgid "Waiting extraction"
msgstr ""

#. module: iap_extract
#: model:ir.model.fields.selection,name:iap_extract.selection__account_bank_statement__extract_state__waiting_validation
#: model:ir.model.fields.selection,name:iap_extract.selection__account_move__extract_state__waiting_validation
#: model:ir.model.fields.selection,name:iap_extract.selection__extract_mixin__extract_state__waiting_validation
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_candidate__extract_state__waiting_validation
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_expense__extract_state__waiting_validation
msgid "Waiting validation"
msgstr "Čakanje na potrditev"

#. module: iap_extract
#. odoo-javascript
#: code:addons/iap_extract/static/src/components/status_header/status.xml:0
msgid "You don't have enough credit to extract data from your document."
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid ""
"Your PDF file is protected by a password. The OCR can't extract data from it"
msgstr ""

#. module: iap_extract
#. odoo-python
#: code:addons/iap_extract/models/extract_mixin.py:0
msgid "Your document contains too many pages"
msgstr ""

#. module: iap_extract
#: model:ir.model.fields.selection,name:iap_extract.selection__account_bank_statement__extract_state__extract_not_ready
#: model:ir.model.fields.selection,name:iap_extract.selection__account_move__extract_state__extract_not_ready
#: model:ir.model.fields.selection,name:iap_extract.selection__extract_mixin__extract_state__extract_not_ready
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_candidate__extract_state__extract_not_ready
#: model:ir.model.fields.selection,name:iap_extract.selection__hr_expense__extract_state__extract_not_ready
msgid "waiting extraction, but it is not ready"
msgstr ""
